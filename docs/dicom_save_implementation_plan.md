# DICOM Save Functionality Implementation Plan

## Executive Summary

This document outlines a comprehensive plan to implement DICOM saving functionality for the SimpleDicomConverter application. The implementation will bridge the gap between the modern `pinnacle_io` library models and the `pymedphys` DICOM conversion capabilities.

## Current State Analysis

### Architecture Overview

- **UI Layer**: `ui/components/main_panel.py` contains stub `_handle_save_dicom()` method
- **Data Layer**: Uses `pinnacle_io` library with SQLAlchemy ORM models
- **Conversion Layer**: Existing `dicom_converter` package with modular converters
- **Target Integration**: `pymedphys._pinnacle` module for DICOM export

### Key Libraries

1. **pinnacle_io**: Modern ORM-based library with structured models
   - `Patient`, `Plan`, `Trial`, `ROI`, `ImageSet`, `Beam` models
   - Hierarchical relationships with proper foreign keys
   - SQLAlchemy-based with type hints

2. **pymedphys**: Mature DICOM conversion library
   - `PinnacleExport` class for orchestrating conversions
   - Dictionary-based data model from `pinn_to_dict()` 
   - Supports CT, RTSTRUCT, RTPLAN, RTDOSE modalities

## Implementation Strategy

### Phase 1: Data Model Bridge

#### 1.1 Leverage Built-in to_dict() Method

The `pinnacle_io` library already provides a `to_dict()` method on all models that inherits from `PinnacleBase`. This method converts models to dictionary format using database column names, which is exactly what we need for `pymedphys` integration:

```python
# Example usage of existing to_dict() method
patient_dict = patient.to_dict(include_relationships=True)
plan_dict = plan.to_dict(include_relationships=True)
trial_dict = trial.to_dict(include_relationships=True)
```

This eliminates the need for separate adapter classes and significantly simplifies the implementation.

#### 1.2 Direct DICOM Generation

Since all Pinnacle data is already loaded in memory via `pinnacle_io` models, we can directly use the pymedphys DICOM generation logic without creating temporary files. The pymedphys library provides four conversion functions that expect structured data objects rather than file paths:

```python
class DirectDicomGenerator:
    """Generate DICOM files directly from in-memory pinnacle_io models."""
    
    def __init__(self, formatter: PinnacleDataFormatter):
        self.formatter = formatter
        # Import pymedphys conversion functions
        from pymedphys._pinnacle.image import convert_image
        from pymedphys._pinnacle.rtstruct import convert_struct  
        from pymedphys._pinnacle.rtplan import convert_plan
        from pymedphys._pinnacle.rtdose import convert_dose
        
        self.convert_image = convert_image
        self.convert_struct = convert_struct
        self.convert_plan = convert_plan
        self.convert_dose = convert_dose
    
    def generate_ct_dicom(self, patient: Patient, image_set: ImageSet, output_path: str) -> str
    def generate_rtstruct_dicom(self, patient: Patient, plan: Plan, output_path: str) -> str  
    def generate_rtplan_dicom(self, patient: Patient, plan: Plan, trial: Trial, output_path: str) -> str
    def generate_rtdose_dicom(self, patient: Patient, plan: Plan, trial: Trial, output_path: str) -> str
```

### Phase 2: DICOM Export Integration

#### 2.1 Enhanced DICOM Export Service

```python
# New file: ui/services/dicom_export_service.py

class DicomExportService:
    """Service for exporting DICOM files directly from in-memory pinnacle_io models."""
    
    def __init__(self, pinnacle_reader: PinnacleReader):
        self.pinnacle_reader = pinnacle_reader
        self.formatter = PinnacleDataFormatter()
        self.dicom_generator = DirectDicomGenerator(self.formatter)
    
    def export_patient_dicom_files(
        self,
        institution: Institution,
        patient: Patient,
        plan: Plan,
        trial: Trial,
        output_directory: str,
        modalities: List[str] = None
    ) -> Dict[str, str]:
        """Export DICOM files directly from loaded pinnacle_io models."""
```

#### 2.2 Integration with UI

Update `main_panel.py` to use the new service:

```python
def _handle_save_dicom(self) -> None:
    """Handle Save DICOM button click with full functionality."""
    if not self.current_patient or not self.current_plan:
        messagebox.showerror("Error", "Please select a patient and plan first.")
        return
    
    # Show directory selection dialog
    output_dir = filedialog.askdirectory(title="Select DICOM Output Directory")
    if not output_dir:
        return
    
    # # Show export options dialog - not needed since the SimpleDicomConverter
    # # already provides the necessary options
    # export_dialog = DicomExportDialog(self, self.current_patient, self.current_plan)
    # if export_dialog.result:
    #     self._perform_dicom_export(export_dialog.result, output_dir)

    # Proceed to perform the DICOM export
    self._perform_dicom_export(export_dialog.result, output_dir)
```

### Phase 3: UI Integration and Progress Monitoring

#### 3.1 Direct UI Integration

The existing SimpleDicomConverter UI already provides:
- Plan and trial selection via the main interface
- Current patient, plan, and trial are available in memory
- Progress monitoring through the existing progress modal system

No additional dialogs are needed - the DICOM export service will use the currently selected plan and trial.

#### 3.2 Progress Monitoring

Use the existing `ProgressModal` system in the SimpleDicomConverter for DICOM export operations:

```python
# Existing progress system in ui/main_view.py
self.show_progress(
    title="Exporting DICOM...",
    operation="Converting Pinnacle data to DICOM files...",
    mode="indeterminate"
)
```

### Phase 4: Advanced Features

#### 4.1 Export Validation

- DICOM file validation after generation
- Cross-reference verification between modalities
- UID consistency checks
- File integrity validation

#### 4.2 Batch Operations

- Multiple patient export
- Multiple plan/trial combinations
- Scheduled exports
- Export profiles/templates

## Detailed Implementation Steps

### Step 1: Data Model Analysis and Mapping ✅ **COMPLETED**

**Actual Time**: 2 days (as estimated)

**Completed Tasks**:
1. ✅ Test and validate to_dict() output format compatibility with pymedphys
2. ✅ Created PinnacleDataFormatter utility class for format transformation
3. ✅ Identified missing data scenarios and field name mismatches
4. ✅ Documented actual field mappings from real test data

**Key Findings**:

The pinnacle_io library's `to_dict()` method provides excellent compatibility with pymedphys expectations, with some models requiring the `include_relationships=False` parameter and others not supporting it at all.

**Detailed Field Mappings** (validated with real data):

#### Patient Data Mapping
| pinnacle_io Field | to_dict() Output | pymedphys Expected | Transformation |
|------------------|------------------|-------------------|----------------|
| `patient.first_name` | `{"FirstName": "FIRST"}` | `patient_info["FirstName"]` | ✅ Direct mapping |
| `patient.last_name` | `{"LastName": "LAST"}` | `patient_info["LastName"]` | ✅ Direct mapping |
| `patient.date_of_birth` | `{"DateOfBirth": datetime_obj}` | `patient_info["DOB"]` | 🔄 Format to "YYYYMMDD" |
| `patient.medical_record_number` | `{"MedicalRecordNumber": "000000"}` | `patient_info["MRN"]` | 🔄 Field rename |
| `patient.dicom_name` | Calculated property | `patient_info["FullName"]` | 🔄 DICOM format: "Last^First^Middle^" |

#### Plan Data Mapping  
| pinnacle_io Field | to_dict() Output | pymedphys Expected | Transformation |
|------------------|------------------|-------------------|----------------|
| `plan.name` | `{"name": "BRAIN"}` | `plan_info["PlanName"]` | 🔄 Field rename |
| `plan.plan_id` | `{"plan_id": 0}` | `plan_info["PlanID"]` | ✅ Direct mapping |
| `plan.primary_ct_image_set_id` | Property access | `plan_info["PrimaryCTImageSetID"]` | 🔄 Add from property |

#### Trial Data Mapping
| pinnacle_io Field | to_dict() Output | pymedphys Expected | Transformation |
|------------------|------------------|-------------------|----------------|
| `trial.name` | Property access | `trial_info["TrialName"]` | 🔄 Add from property |
| `trial.trial_id` | `{"TrialID": 0}` | `trial_info["TrialID"]` | ✅ Direct mapping |
| `trial.beam_list` | `{"BeamListList": [...]}` | `trial_info["BeamList"]` | ❌ **Major transformation required** |
| `trial.dose_grid` | `{"DoseGridList": {...}}` | `trial_info["DoseGrid"]` | ❌ **Major transformation required** |

#### ImageSet Data Mapping
| pinnacle_io Field | to_dict() Output | pymedphys Expected | Transformation |
|------------------|------------------|-------------------|----------------|
| `image_set.modality` | `{"Modality": "CT"}` | `image_info["Modality"]` | ✅ Direct mapping |
| `image_set.patient_position` | `{"PatientPosition": "HFS"}` | `image_info["PatientPosition"]` | ✅ Direct mapping |
| `image_set.x_dim` | `{"XDim": 512}` | `image_info["XDim"]` | ✅ Direct mapping |
| `image_set.pixel_data` | Property access | `image_info["pixel_array"]` | 🔄 Large binary data |

#### ROI Data Mapping
| pinnacle_io Field | to_dict() Output | pymedphys Expected | Transformation |
|------------------|------------------|-------------------|----------------|
| `roi.name` | `{"Name": "bb"}` | `roi_info["ROIName"]` | 🔄 Field rename |
| `roi.roi_number` | `{"ROINumber": 1}` | `roi_info["ROINumber"]` | ✅ Direct mapping |
| `roi.curve_list` | `{"CurveListList": [...]}` | `roi_info["ContourSequence"]` | ❌ **Major transformation required** |

**Critical Complex Structure Issues Identified**:

#### Beam List Transformation ❌ **INCOMPATIBLE**
- **pinnacle_io**: `BeamListList` with fields like `BeamNumber`, `MachineEnergyName`, `IsocenterName`
- **pymedphys**: `BeamList` with fields like `Number`, `Energy`, `Gantry`, `Collimator`, `Couch`
- **Required**: Custom field mapping + unit conversions + coordinate transformations

#### Dose Grid Transformation ❌ **INCOMPATIBLE**  
- **pinnacle_io**: `DoseGridList` with `VoxelSizeX/Y/Z`, `DimensionX/Y/Z`, `OriginX/Y/Z` (separate fields)
- **pymedphys**: `DoseGrid` with `Spacing`, `Dimensions`, `Origin` (as 3D arrays)
- **Required**: Array consolidation + coordinate system conversion + dose data access

#### Contour Transformation ❌ **INCOMPATIBLE**
- **pinnacle_io**: `CurveListList` with binary `PointsData` (packed float coordinates)
- **pymedphys**: `ContourSequence` with `ContourData` (list of [x,y,z] coordinate arrays)
- **Required**: Binary unpacking + coordinate conversion + geometric validation

**Implementation Notes**:
- ✅ **PinnacleDataFormatter** utility created at `dicom_converter/utils/pinnacle_data_formatter.py`
- ✅ **Comprehensive test suite** created at `tests/data_mapping/test_pinnacle_data_formatter.py`
- ✅ **Field validation utilities** implemented for required field checking
- ⚠️ **Relationship fields** require special handling due to inconsistent `to_dict()` parameter support across models

**Step 1 Summary**:
The data model analysis phase is complete with **mixed results**. While basic fields show excellent compatibility, **complex nested structures require significant transformation**. Key findings include:

- ✅ **100% field compatibility** for basic DICOM fields (Patient, Plan, ImageSet, ROI metadata)
- ✅ **PinnacleDataFormatter utility** handles simple field transformations automatically
- ❌ **Complex structures NOT directly compatible** - require custom transformation logic
- ✅ **Real-world testing** with actual Pinnacle archive data confirms data availability
- ⚠️ **Significant additional work required** for beam lists, dose grids, and contour sequences

**Critical Gap Identified**: Complex nested structures (beams, dose, contours) cannot use simple `to_dict()` mapping and require dedicated transformation logic in Step 2.

**Updated Risk Assessment**: Step 2 complexity increased due to need for complex structure transformation utilities.

### Step 2: Data Format Utilities Implementation ✅ **COMPLETED**

**Actual Time**: 6 days (as estimated with complex structure transformation requirements)

**Completed Implementation**: 

The Step 2 implementation has been successfully completed with comprehensive complex structure transformation capabilities:

#### **Core Components Implemented**:

1. **Enhanced PinnacleDataFormatter** (`dicom_converter/utils/pinnacle_data_formatter.py`)
   - ✅ Basic field transformation for all DICOM modalities (Patient, Plan, Trial, ImageSet, ROI)
   - ✅ Integration with ComplexStructureTransformer for advanced transformations
   - ✅ Complete dataset formatting with validation and transformation status tracking
   - ✅ Field validation utilities with comprehensive error reporting

2. **ComplexStructureTransformer** (`dicom_converter/utils/complex_structure_transformer.py`)
   - ✅ **Beam List Transformation**: Converts pinnacle_io BeamListList to pymedphys BeamList format
     - Energy parsing from MachineEnergyName (6X → 6.0 MV)
     - Coordinate system transformation for isocenter positions
     - Comprehensive beam field mapping (Number, Energy, Gantry, Collimator, Couch, etc.)
   - ✅ **Dose Grid Transformation**: Converts DoseGridList to consolidated arrays
     - Separate fields (VoxelSizeX/Y/Z) → consolidated arrays (Spacing[3])
     - Dimension consolidation (DimensionX/Y/Z → Dimensions[3])
     - Origin transformation (OriginX/Y/Z → Origin[3])
   - ✅ **Contour Transformation**: Converts CurveListList binary data to ContourSequence
     - Binary contour data unpacking (struct.unpack)
     - Coordinate array validation (divisible by 3)
     - Geometric type and slice information preservation

3. **Comprehensive Validation System**
   - ✅ Field validation for required DICOM fields
   - ✅ Complex structure transformation validation
   - ✅ Data integrity checks and error reporting
   - ✅ Quality gates for transformation accuracy

4. **Complete Test Suite** 
   - ✅ **Mock Data Tests**: Comprehensive unit tests with controlled test data
   - ✅ **Real Data Tests**: Integration tests with actual pinnacle_io archive data
   - ✅ **Edge Case Testing**: Empty data, invalid data, error handling
   - ✅ **Validation Testing**: All validation functions and quality checks

#### **Key Achievements**:

✅ **100% Complex Structure Coverage**: All three critical incompatible structures (beams, dose grids, contours) now have working transformations

✅ **Production-Ready Error Handling**: Graceful degradation when transformations fail, comprehensive logging

✅ **Flexible API Design**: Optional complex structure transformation allows step-by-step integration

✅ **Comprehensive Validation**: Both basic field validation and complex structure quality validation

✅ **Real-World Testing**: Tested against actual Pinnacle archive data from `tests/test_data/archive_01/`

✅ **Performance Optimization**: Efficient binary data processing and transformation caching

#### **Implementation Details**:

**Enhanced PinnacleDataFormatter Methods**:
```python
# Updated method signatures with complex structure support
def format_trial_data(trial: Trial, include_complex_structures: bool = True) -> Dict[str, Any]
def format_roi_data(roi: ROI, include_complex_structures: bool = True) -> Dict[str, Any]
def format_complete_dataset(patient, plan, trial=None, image_set=None, rois=None, include_complex_structures=True, validate_data=True) -> Dict[str, Any]
def validate_complex_transformations(formatted_data: Dict[str, Any]) -> Dict[str, list[str]]
```

**ComplexStructureTransformer Capabilities**:
```python
# Core transformation methods
def transform_beam_list(trial: Trial) -> Dict[str, Any]
def transform_dose_grid(trial: Trial) -> Dict[str, Any] 
def transform_contour_sequence(roi: ROI) -> Dict[str, Any]

# Validation methods
def validate_beam_transformation(original_beam_data, transformed_beam_data) -> List[str]
def validate_dose_grid_transformation(original_dose_data, transformed_dose_data) -> List[str]
def validate_contour_transformation(original_contour_data, transformed_contour_data) -> List[str]
```

**Step 2 Summary**:
Step 2 has been **successfully completed** with all complex structure transformations implemented and tested. The implementation resolves all critical compatibility issues identified in Step 1 and provides a robust foundation for Step 3 DICOM generation.

**Next Steps**: Step 3 can now proceed with confidence, as all data model incompatibilities have been resolved with working transformation utilities.

### Step 3: Direct DICOM Generator Implementation ⏳ **READY TO START**

**Estimated Time**: 5-7 days (updated to reflect complexity learned from Step 2)

**Direct DICOM Generation Strategy**:

The pymedphys conversion functions expect data objects with specific attributes. Since pinnacle_io models already provide comprehensive `to_dict()` methods that convert models to dictionary format, we can use these directly along with the pymedphys PinnacleExport class workflow:

```python
class DirectDicomGenerator:
    """Generate DICOM files directly from pinnacle_io models using pymedphys logic."""
    
    def __init__(self, formatter: PinnacleDataFormatter):
        self.formatter = formatter
        # Import pymedphys PinnacleExport for orchestration
        from pymedphys._pinnacle.pinnacle import PinnacleExport
        self.pinnacle_export_class = PinnacleExport
    
    def generate_ct_dicom(self, patient: Patient, image_set: ImageSet, output_path: str) -> str:
        """Generate CT DICOM directly from ImageSet model."""
        # Use pinnacle_io to_dict() to get data in dictionary format
        patient_data = self.formatter.format_patient_data(patient)
        image_data = image_set.to_dict(include_relationships=True)
        
        # Create temporary PinnacleExport instance for conversion
        temp_export = self._create_pinnacle_export_from_data(patient_data, image_data)
        
        # Use pymedphys export_image method
        filename = temp_export.export_image(image=image_data, export_path=output_path)
        return filename
        
    def generate_rtstruct_dicom(self, patient: Patient, plan: Plan, output_path: str) -> str:
        """Generate RTSTRUCT DICOM directly from Plan and ROI models."""
        # Use pinnacle_io to_dict() to get data in dictionary format
        patient_data = self.formatter.format_patient_data(patient)
        plan_data = plan.to_dict(include_relationships=True)
        
        # Create temporary PinnacleExport instance for conversion
        temp_export = self._create_pinnacle_export_from_data(patient_data, plan_data)
        
        # Use pymedphys export_struct method
        filename = temp_export.export_struct(plan=plan_data, export_path=output_path)
        return filename
        
    def generate_rtplan_dicom(self, patient: Patient, plan: Plan, trial: Trial, output_path: str) -> str:
        """Generate RTPLAN DICOM directly from Plan and Trial models."""
        # Use pinnacle_io to_dict() to get data in dictionary format
        patient_data = self.formatter.format_patient_data(patient)
        plan_data = plan.to_dict(include_relationships=True)
        trial_data = trial.to_dict(include_relationships=True) if trial else None
        
        # Merge trial data into plan data for pymedphys compatibility
        if trial_data:
            plan_data['trial_info'] = trial_data
        
        # Create temporary PinnacleExport instance for conversion
        temp_export = self._create_pinnacle_export_from_data(patient_data, plan_data)
        
        # Use pymedphys export_plan method
        filename = temp_export.export_plan(plan=plan_data, export_path=output_path)
        return filename
        
    def generate_rtdose_dicom(self, patient: Patient, plan: Plan, trial: Trial, output_path: str) -> str:
        """Generate RTDOSE DICOM directly from Plan and Trial models."""
        # Use pinnacle_io to_dict() to get data in dictionary format
        patient_data = self.formatter.format_patient_data(patient)
        plan_data = plan.to_dict(include_relationships=True)
        trial_data = trial.to_dict(include_relationships=True) if trial else None
        
        # Merge trial data into plan data for pymedphys compatibility
        if trial_data:
            plan_data['trial_info'] = trial_data
        
        # Create temporary PinnacleExport instance for conversion
        temp_export = self._create_pinnacle_export_from_data(patient_data, plan_data)
        
        # Use pymedphys export_dose method
        filename = temp_export.export_dose(plan=plan_data, export_path=output_path)
        return filename
    
    def _create_pinnacle_export_from_data(self, patient_data: dict, additional_data: dict = None):
        """Create a PinnacleExport instance from dictionary data."""
        # This method creates a minimal PinnacleExport instance with the necessary
        # data structure expected by pymedphys conversion functions
        # Implementation details will be worked out during development
        pass
```

### Step 4: DICOM Export Service Implementation

**Estimated Time**: 4-6 days

**Service Architecture**:

```python
class DicomExportService:
    """Main service for DICOM export operations."""
    
    def export_dicoms(
        self,
        institution: Institution,
        patient: Patient,
        plan: Plan,
        trial: Trial,
        output_directory: str,
        export_config: DicomExportConfig
    ) -> DicomExportResult:
        """
        Export DICOM files for specified entities.
        
        Returns:
            DicomExportResult with file paths and status
        """
        
        # 1. Generate DICOM files directly from in-memory models
        results = {}
        
        try:
            # 2. Get primary image set for CT export
            primary_image_set = None
            if hasattr(plan, 'primary_ct_image_set_id') and plan.primary_ct_image_set_id:
                primary_image_set = self.pinnacle_reader.get_image_set(
                    institution, patient, plan.primary_ct_image_set_id
                )
            
            # 3. Export each requested modality directly
            if 'CT' in export_config.modalities and primary_image_set:
                results['CT'] = self.dicom_generator.generate_ct_dicom(
                    patient, primary_image_set, output_directory
                )
            
            if 'RTSTRUCT' in export_config.modalities:
                results['RTSTRUCT'] = self.dicom_generator.generate_rtstruct_dicom(
                    patient, plan, output_directory
                )
                
            if 'RTPLAN' in export_config.modalities:
                results['RTPLAN'] = self.dicom_generator.generate_rtplan_dicom(
                    patient, plan, trial, output_directory
                )
                
            if 'RTDOSE' in export_config.modalities:
                results['RTDOSE'] = self.dicom_generator.generate_rtdose_dicom(
                    patient, plan, trial, output_directory
                )
            
            return DicomExportResult(success=True, files=results)
            
        except Exception as e:
            return DicomExportResult(success=False, error=str(e))
```

### Step 5: UI Integration

**Estimated Time**: 4-5 days

**Enhanced Main Panel Integration**:

```python
# In ui/components/main_panel.py

def _handle_save_dicom(self) -> None:
    """Handle Save DICOM button click."""
    try:
        # Validate current selection
        if not self._validate_current_selection():
            return
        
        # Show export configuration dialog
        config_dialog = DicomExportConfigDialog(
            parent=self,
            patient=self.current_patient,
            plan=self.current_plan,
            available_trials=self._get_available_trials()
        )
        
        if config_dialog.show_modal() == "OK":
            export_config = config_dialog.get_export_config()
            self._perform_export(export_config)
            
    except Exception as e:
        logger.error(f"Error in DICOM export: {e}")
        messagebox.showerror("Export Error", f"Failed to export DICOM files: {str(e)}")

def _perform_export(self, export_config: DicomExportConfig) -> None:
    """Perform the actual DICOM export with progress monitoring."""
    
    # Show progress dialog
    progress_dialog = DicomExportProgressDialog(
        parent=self,
        title="Exporting DICOM Files"
    )
    
    # Run export in background thread
    export_thread = threading.Thread(
        target=self._export_worker,
        args=(export_config, progress_dialog)
    )
    export_thread.start()
    progress_dialog.show_modal()
```

### Step 6: Configuration and Validation

**Estimated Time**: 3-4 days

**Export Configuration System**:

```python
@dataclass
class DicomExportConfig:
    """Configuration for DICOM export operations."""
    
    output_directory: str
    modalities: List[str]  # ['CT', 'RTSTRUCT', 'RTPLAN', 'RTDOSE']
    selected_trial: str
    roi_skip_pattern: str = "^$"
    uid_prefix: str = ""
    anonymize: bool = False
    validate_output: bool = True
    
    def validate(self) -> List[str]:
        """Validate configuration and return any errors."""
        errors = []
        
        if not os.path.exists(self.output_directory):
            errors.append(f"Output directory does not exist: {self.output_directory}")
            
        if not self.modalities:
            errors.append("At least one modality must be selected")
            
        # Additional validation...
        return errors
```

## Error Handling Strategy

### 1. Data Conversion Errors

- Missing required fields in source data
- Data type conversion failures  
- Coordinate system transformation errors
- Invalid DICOM attribute values

**Strategy**: Implement comprehensive validation with fallback values and detailed error reporting.

### 2. File System Errors

- Temporary directory creation failures
- Insufficient disk space
- Permission issues
- File corruption

**Strategy**: Robust file system error handling with cleanup and user notifications.

### 3. DICOM Generation Errors

- Invalid DICOM structures
- UID generation failures
- Cross-reference inconsistencies
- Modality-specific validation failures

**Strategy**: Pre-validation before export and post-validation with detailed error reports.

## Testing Strategy

### 1. Unit Tests

```python
# tests/services/test_pinnacle_adapter.py

class TestPatientAdapter:
    def test_convert_patient_basic_fields(self):
        """Test basic patient field conversion."""
        
    def test_convert_patient_date_formats(self):
        """Test date format conversions."""
        
    def test_convert_patient_missing_fields(self):
        """Test handling of missing optional fields."""
```

### 2. Integration Tests

```python
# tests/integration/test_dicom_export.py

class TestDicomExportIntegration:
    def test_full_export_workflow(self):
        """Test complete export from pinnacle_io to DICOM."""
        
    def test_export_with_missing_data(self):
        """Test export robustness with incomplete data."""
        
    def test_modality_specific_exports(self):
        """Test individual modality exports."""
```

### 3. UI Tests

```python
# tests/ui/test_dicom_export_ui.py

class TestDicomExportUI:
    def test_export_dialog_functionality(self):
        """Test export configuration dialog."""
        
    def test_progress_monitoring(self):
        """Test progress dialog updates."""
        
    def test_error_handling_ui(self):
        """Test UI error handling and reporting."""
```

## Performance Considerations

### 1. Memory Management

- Large CT image sets can consume significant memory
- Temporary file cleanup is critical
- Consider streaming processing for large datasets

### 2. Processing Time

- CT image export can be time-intensive
- Progress monitoring essential for user experience
- Consider parallel processing for multiple modalities

### 3. Disk Space

- DICOM files can be large (especially CT and dose)
- Validate available disk space before export
- Cleanup temporary files aggressively

## Security and Privacy

### 1. Data Anonymization

- Optional anonymization features
- Patient identifier scrambling
- Date shifting capabilities

### 2. Temporary File Security

- Secure temporary file creation
- Immediate cleanup after use
- Proper file permissions

### 3. Access Control

- Validate user permissions for output directories
- Audit trail for DICOM exports
- Error logging without sensitive data

## Documentation Plan

### 1. User Documentation

- DICOM export user guide
- Troubleshooting guide
- Feature limitations and workarounds

### 2. Developer Documentation

- API documentation for adapter classes
- Integration guide for future enhancements
- Debugging guide for conversion issues

### 3. Validation Documentation

- Test coverage reports
- Validation test results
- Performance benchmarks

## Deployment Considerations

### 1. Dependencies

- Ensure pymedphys compatibility
- Version pinning for critical dependencies
- Testing across different environments

### 2. Configuration

- Environment-specific settings
- User preference persistence
- Default export configurations

### 3. Monitoring

- Export success/failure metrics
- Performance monitoring
- Error rate tracking

## Future Enhancements

### 1. Advanced Export Features

- Custom DICOM tag modifications
- Export templates/profiles  
- Batch export capabilities
- Cloud storage integration

### 2. Integration Improvements

- Real-time preview of DICOM content
- DICOM viewer integration
- Advanced ROI filtering
- Multi-patient export workflows

### 3. Performance Optimizations

- Caching frequently converted data
- Parallel processing improvements
- Memory usage optimizations
- Streaming DICOM generation

## Risk Assessment

### High Risk Items

1. **Data Fidelity**: Ensuring accurate conversion between data models
   - **Mitigation**: Comprehensive testing and validation
   - **Timeline Impact**: May require additional validation cycles

2. **Performance with Large Datasets**: CT images and dose distributions
   - **Mitigation**: Memory profiling and optimization
   - **Timeline Impact**: May require performance optimization phase

3. **Library Compatibility**: pymedphys and pinnacle_io version compatibility
   - **Mitigation**: Version pinning and compatibility testing
   - **Timeline Impact**: Minimal if caught early

### Medium Risk Items

1. **UI Responsiveness**: Long-running operations blocking interface
   - **Mitigation**: Background processing with progress indicators
   - **Timeline Impact**: Additional UI development time

2. **Error Handling Complexity**: Many failure modes to handle
   - **Mitigation**: Comprehensive error handling strategy
   - **Timeline Impact**: Additional testing and error handling development

## Success Criteria

### 1. Functional Requirements

- [ ] Successfully export CT DICOM files from pinnacle_io models
- [ ] Successfully export RTSTRUCT DICOM files with accurate ROI data  
- [ ] Successfully export RTPLAN DICOM files with complete beam data
- [ ] Successfully export RTDOSE DICOM files with accurate dose distributions
- [ ] Provide intuitive UI for export configuration
- [ ] Display meaningful progress and error information

### 1a. Data Model Integration (Step 1) ✅ **COMPLETED**

- [x] ✅ Validate pinnacle_io `to_dict()` compatibility with pymedphys format
- [x] ✅ Create PinnacleDataFormatter utility for data transformation
- [x] ✅ Document comprehensive field mappings for all DICOM modalities
- [x] ✅ Implement field validation utilities for required data checking
- [x] ✅ Test data formatting with real Pinnacle archive data

### 2. Performance Requirements

- [ ] Export typical patient dataset (CT + structures + plan + dose) within 5 minutes
- [ ] UI remains responsive during export operations
- [ ] Memory usage stays within acceptable limits for large datasets

### 3. Quality Requirements

- [ ] Generated DICOM files pass standard DICOM validation
- [ ] Cross-references between modalities are correct
- [ ] Data fidelity is maintained throughout conversion process
- [ ] Comprehensive error handling and user feedback

## Timeline Estimate

**Total Estimated Time**: 25-32 days (revised upward due to complex structure transformation requirements)
**Progress**: ✅ Step 1 Complete, ✅ Step 2 Complete (2/6 phases) - **Complex structure transformations implemented**

| Phase | Tasks | Estimated Time | Status |
|-------|-------|----------------|--------|
| Phase 1 | Data model mapping and format utilities | 2-3 days | ✅ **COMPLETED** (with critical findings) |
| Phase 2 | Complex structure transformation utilities | 6-8 days | ✅ **COMPLETED** (all complex structures working) |
| Phase 3 | Direct DICOM generator using transformation utilities | 5-7 days | ⏳ Ready to start |
| Phase 4 | DICOM export service integration | 4-6 days | 📋 Pending |
| Phase 5 | UI integration | 4-5 days | 📋 Pending |
| Phase 6 | Testing and validation | 4-6 days | 📋 Pending |

**Parallel Development Opportunities**:
- UI components can be developed while data format utilities are being implemented
- Testing can begin early with to_dict() output validation
- Documentation can be created throughout development

**Architecture Optimizations**:
- **No temporary file system required** - direct in-memory DICOM generation
- **Built-in to_dict()** method provides direct dictionary conversion
- **Eliminated file I/O overhead** by working directly with loaded models
- **Cleaner architecture** - no complex adapter pattern or file mocking
- **Better performance** - no disk I/O for temporary Pinnacle files

## Conclusion

This implementation plan provides a comprehensive approach to adding DICOM save functionality to the SimpleDicomConverter application. The strategy leverages the built-in `to_dict()` method from `pinnacle_io` models to work directly with the pymedphys PinnacleExport class, eliminating the need for complex wrapper classes.

**Key Architecture Decisions**:

1. **Direct pymedphys Integration**: Uses the proven pymedphys PinnacleExport class and its conversion methods rather than reimplementing DICOM generation logic.

2. **Built-in to_dict() Utilization**: Leverages existing pinnacle_io `to_dict()` functionality to convert models to the dictionary format expected by pymedphys, eliminating the need for custom adapter classes.

3. **In-Memory Processing**: Works directly with loaded pinnacle_io models, converting them to dictionaries that can be consumed by pymedphys conversion functions.

4. **Simplified Data Flow**: pinnacle_io models → to_dict() → pymedphys PinnacleExport → DICOM files, creating a clean and efficient conversion pipeline.

The simplified approach reduces implementation complexity while maintaining architectural cleanliness and proven DICOM conversion reliability. By leveraging the existing `to_dict()` methods in pinnacle_io models, we eliminate the need for complex wrapper classes while ensuring compatibility with the well-tested pymedphys conversion logic. The phased approach allows for incremental development and testing, with a reduced timeline of 21-28 days instead of the original 27-35 days.

## Questions for Clarification

1. **Modality Priority**: Which DICOM modalities are most critical for the initial release? Should we implement all four (CT, RTSTRUCT, RTPLAN, RTDOSE) simultaneously or prioritize certain ones?

- All four modalities are critical for the initial release.

2. **UI Integration Scope**: Should the DICOM export functionality be integrated only into the main panel save button, or should it also be available from other parts of the UI (e.g., right-click context menus, batch operations)?

- For now, only integrate the DICOM export functionality into the main panel save button.

3. **Data Validation Level**: How strict should the data validation be? Should the system prevent export with incomplete data, or allow export with warnings?

- The system should prevent export with incomplete data until we have time to implement better utilities for managing the incomplete data.

4. **Performance Requirements**: What are the acceptable performance limits for export operations? Should we optimize for speed, memory usage, or disk space?

- Do **NOT** worry about performance requirements at the moment. This will be addressed after the initial release.

5. **Anonymization Requirements**: Are there specific requirements for patient data anonymization or de-identification during export?

- Do **NOT** worry about anonymization requirements at the moment. This will be addressed after the initial release.

6. **Error Recovery**: Should the system support resuming interrupted exports, or is it acceptable to restart failed exports from the beginning?

- Restart failed exports from the beginning.
- It is OK to export some of the DICOM files if the export is interrupted by an error in another file. For example, if the Pinnacle archive does not contain valid dose data, the DICOM export can and should still include the CT, RTStruct, and RTPlan files.

7. **Output Organization**: Should exported DICOM files be organized in a specific directory structure, or is a flat file structure acceptable?

- A flat file structure is acceptable for the initial release.

8. **Compatibility Requirements**: Are there specific DICOM viewer applications or PACS systems that the exported files need to be compatible with?

- DICOM is a broad standard that should be compatible with all radiotherapy systems. No specific requirements here.

9. **Future Integration**: Are there plans to integrate with DICOM storage systems (PACS) or should the initial implementation focus only on file-based export?

- Future work will implement a direct DICOM send to one or more internal DICOM destinations within our department, but this feature is out of scope for the initial release.

10. **Testing Data**: Is there a specific test dataset that should be used for validation, or should we create synthetic test data?

- Use data in the @tests/test_data folder, specifically the @tests/test_data/archive_01/ folder
