"""
Complex Structure Transformer for pinnacle_io to pymedphys compatibility.

This module handles the complex structure transformations that cannot use simple 
to_dict() mapping, including beam lists, dose grids, and contour sequences.
"""

import struct
import numpy as np
from typing import Dict, Any, List, Tuple, Optional
import logging

from pinnacle_io.models import Trial, ROI, DoseGrid, Beam

logger = logging.getLogger(__name__)


class ComplexStructureTransformer:
    """Handles complex structure transformations for pymedphys compatibility."""

    @staticmethod
    def transform_beam_list(trial: Trial) -> Dict[str, Any]:
        """
        Transform pinnacle_io beam list to pymedphys BeamList format.
        
        Args:
            trial: Trial model containing beam data
            
        Returns:
            Dictionary with transformed beam list compatible with pymedphys
        """
        if not hasattr(trial, 'beam_list') or not trial.beam_list:
            return {"BeamList": [], "BeamCount": 0}
        
        transformed_beams = []
        
        for beam in trial.beam_list:
            try:
                transformed_beam = ComplexStructureTransformer._transform_single_beam(beam)
                transformed_beams.append(transformed_beam)
            except Exception as e:
                logger.warning(f"Failed to transform beam {getattr(beam, 'BeamNumber', 'unknown')}: {e}")
                # Continue with other beams
                continue
        
        return {
            "BeamList": transformed_beams,
            "BeamCount": len(transformed_beams)
        }

    @staticmethod
    def _transform_single_beam(beam: Beam) -> Dict[str, Any]:
        """
        Transform a single beam from pinnacle_io to pymedphys format.
        
        Args:
            beam: Single beam from pinnacle_io
            
        Returns:
            Transformed beam dictionary
        """
        beam_dict = beam.to_dict() if hasattr(beam, 'to_dict') else {}
        
        # Map pinnacle_io fields to pymedphys expected fields
        transformed = {
            # Basic beam identification
            "Number": beam_dict.get("BeamNumber", 0),
            "Name": beam_dict.get("Name", f"Beam {beam_dict.get('BeamNumber', 0)}"),
            
            # Energy mapping (convert MachineEnergyName to numeric energy)
            "Energy": ComplexStructureTransformer._parse_energy(
                beam_dict.get("MachineEnergyName", "6")
            ),
            
            # Gantry angle (direct mapping if available)
            "Gantry": beam_dict.get("GantryAngle", 0.0),
            
            # Collimator angle (direct mapping if available)  
            "Collimator": beam_dict.get("CollimatorAngle", 0.0),
            
            # Couch angle (may need coordinate transformation)
            "Couch": beam_dict.get("CouchAngle", 0.0),
            
            # Isocenter position (coordinate transformation required)
            "Isocenter": ComplexStructureTransformer._transform_isocenter(beam_dict),
            
            # MLC leaf positions (if present)
            "MLCPositions": ComplexStructureTransformer._transform_mlc_positions(beam_dict),
            
            # Dose rate and other parameters
            "DoseRate": beam_dict.get("DoseRate", 600),
            "TreatmentTime": beam_dict.get("TreatmentTime", 0.0),
            "MonitorUnits": beam_dict.get("MonitorUnits", 0.0),
            
            # Beam type and modality
            "BeamType": beam_dict.get("BeamType", "STATIC"),
            "Modality": beam_dict.get("Modality", "PHOTON")
        }
        
        return transformed

    @staticmethod
    def _parse_energy(energy_name: str) -> float:
        """
        Extract numeric energy from MachineEnergyName.
        
        Args:
            energy_name: String like "6X", "18X", "6E", etc.
            
        Returns:
            Numeric energy in MV
        """
        try:
            # Extract numeric part from energy name
            import re
            match = re.search(r'(\d+(?:\.\d+)?)', str(energy_name))
            if match:
                return float(match.group(1))
            else:
                logger.warning(f"Could not parse energy from: {energy_name}")
                return 6.0  # Default fallback
        except Exception:
            return 6.0

    @staticmethod
    def _transform_isocenter(beam_dict: Dict[str, Any]) -> List[float]:
        """
        Transform isocenter coordinates from pinnacle to DICOM coordinate system.
        
        Args:
            beam_dict: Beam dictionary data
            
        Returns:
            [x, y, z] coordinates in DICOM coordinate system
        """
        # Extract isocenter information
        isocenter_name = beam_dict.get("IsocenterName", "")
        
        # Try to get coordinates from various possible fields
        x = beam_dict.get("IsocenterX", beam_dict.get("IsoX", 0.0))
        y = beam_dict.get("IsocenterY", beam_dict.get("IsoY", 0.0))  
        z = beam_dict.get("IsocenterZ", beam_dict.get("IsoZ", 0.0))
        
        # Convert from Pinnacle coordinate system to DICOM coordinate system
        # Note: This may require patient position-specific transformations
        return [float(x), float(y), float(z)]

    @staticmethod
    def _transform_mlc_positions(beam_dict: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Transform MLC leaf positions if present.
        
        Args:
            beam_dict: Beam dictionary data
            
        Returns:
            MLC position data or None if not available
        """
        # Check for MLC data
        if "MLCData" not in beam_dict and "LeafPositions" not in beam_dict:
            return None
        
        # This is a placeholder for MLC transformation
        # Real implementation would need to handle specific MLC models
        return {
            "MLCType": beam_dict.get("MLCType", "Unknown"),
            "LeafPositions": beam_dict.get("LeafPositions", [])
        }

    @staticmethod
    def transform_dose_grid(trial: Trial) -> Dict[str, Any]:
        """
        Transform pinnacle_io dose grid to pymedphys DoseGrid format.
        
        Args:
            trial: Trial model containing dose grid data
            
        Returns:
            Dictionary with transformed dose grid compatible with pymedphys
        """
        if not hasattr(trial, 'dose_grid') or not trial.dose_grid:
            return {"DoseGrid": None, "HasDoseGrid": False}
        
        dose_grid = trial.dose_grid
        
        try:
            # Get base dose grid data
            dose_dict = dose_grid.to_dict() if hasattr(dose_grid, 'to_dict') else {}
            
            # Transform separate dimension fields to consolidated arrays
            transformed = {
                # Consolidated spacing array [x, y, z]
                "Spacing": [
                    dose_dict.get("VoxelSizeX", 1.0),
                    dose_dict.get("VoxelSizeY", 1.0), 
                    dose_dict.get("VoxelSizeZ", 1.0)
                ],
                
                # Consolidated dimensions array [nx, ny, nz]
                "Dimensions": [
                    dose_dict.get("DimensionX", 1),
                    dose_dict.get("DimensionY", 1),
                    dose_dict.get("DimensionZ", 1)
                ],
                
                # Consolidated origin array [x, y, z]
                "Origin": [
                    dose_dict.get("OriginX", 0.0),
                    dose_dict.get("OriginY", 0.0),
                    dose_dict.get("OriginZ", 0.0)
                ],
                
                # Dose units and scaling
                "DoseUnits": dose_dict.get("DoseUnits", "GY"),
                "DoseGridScaling": dose_dict.get("DoseGridScaling", 1.0),
                "DoseType": dose_dict.get("DoseType", "PHYSICAL"),
                
                # Additional metadata
                "HasDoseGrid": True,
                "GridType": "REGULAR"  # Pinnacle typically uses regular grids
            }
            
            # Add dose data access information if available
            if hasattr(dose_grid, 'get_dose_data'):
                transformed["DoseDataAvailable"] = True
                # Note: Actual dose data loading will be handled separately due to size
            else:
                transformed["DoseDataAvailable"] = False
            
            return {"DoseGrid": transformed, "HasDoseGrid": True}
            
        except Exception as e:
            logger.error(f"Failed to transform dose grid: {e}")
            return {"DoseGrid": None, "HasDoseGrid": False}

    @staticmethod
    def transform_contour_sequence(roi: ROI) -> Dict[str, Any]:
        """
        Transform pinnacle_io contour data to pymedphys ContourSequence format.
        
        Args:
            roi: ROI model containing contour data
            
        Returns:
            Dictionary with transformed contour sequence compatible with pymedphys
        """
        if not hasattr(roi, 'curve_list') or not roi.curve_list:
            return {"ContourSequence": [], "ContourCount": 0}
        
        contour_sequence = []
        
        for curve in roi.curve_list:
            try:
                transformed_contour = ComplexStructureTransformer._transform_single_contour(curve)
                if transformed_contour:
                    contour_sequence.append(transformed_contour)
            except Exception as e:
                logger.warning(f"Failed to transform contour: {e}")
                continue
        
        return {
            "ContourSequence": contour_sequence,
            "ContourCount": len(contour_sequence)
        }

    @staticmethod
    def _transform_single_contour(curve) -> Optional[Dict[str, Any]]:
        """
        Transform a single contour curve from pinnacle_io format.
        
        Args:
            curve: Single curve/contour from pinnacle_io
            
        Returns:
            Transformed contour dictionary or None if transformation fails
        """
        try:
            curve_dict = curve.to_dict() if hasattr(curve, 'to_dict') else {}
            
            # Extract points data (binary format in Pinnacle)
            points_data = curve_dict.get("PointsData")
            if not points_data:
                logger.warning("No PointsData found in contour")
                return None
            
            # Unpack binary points data to coordinate arrays
            coordinates = ComplexStructureTransformer._unpack_contour_points(points_data)
            if not coordinates:
                return None
            
            # Transform to pymedphys ContourData format
            transformed = {
                "ContourData": coordinates,  # List of [x, y, z] coordinate arrays
                "NumberOfContourPoints": len(coordinates) // 3,  # Total points
                "ContourGeometricType": curve_dict.get("GeometricType", "CLOSED_PLANAR"),
                
                # Slice-specific information
                "SliceThickness": curve_dict.get("SliceThickness", 1.0),
                "SliceLocation": curve_dict.get("SliceLocation", 0.0),
                
                # Additional metadata
                "ContourNumber": curve_dict.get("CurveID", 0),
                "ContourType": "ROI"
            }
            
            return transformed
            
        except Exception as e:
            logger.error(f"Failed to transform single contour: {e}")
            return None

    @staticmethod
    def _unpack_contour_points(points_data: bytes) -> Optional[List[float]]:
        """
        Unpack binary contour points data to coordinate list.
        
        Args:
            points_data: Binary data containing packed float coordinates
            
        Returns:
            List of coordinates [x1, y1, z1, x2, y2, z2, ...] or None if failed
        """
        try:
            if not points_data or len(points_data) % 4 != 0:
                logger.warning("Invalid points data length")
                return None
            
            # Unpack as little-endian float32 values
            num_floats = len(points_data) // 4
            coordinates = struct.unpack(f'<{num_floats}f', points_data)
            
            # Verify we have complete coordinate triplets
            if len(coordinates) % 3 != 0:
                logger.warning(f"Coordinate count {len(coordinates)} not divisible by 3")
                return None
            
            return list(coordinates)
            
        except struct.error as e:
            logger.error(f"Failed to unpack contour points: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error unpacking contour points: {e}")
            return None

    @staticmethod
    def validate_beam_transformation(original_beam_data: Dict, transformed_beam_data: Dict) -> List[str]:
        """
        Validate beam transformation quality and identify issues.
        
        Args:
            original_beam_data: Original pinnacle_io beam data
            transformed_beam_data: Transformed beam data
            
        Returns:
            List of validation issues (empty if no issues)
        """
        issues = []
        
        # Check required fields are present
        required_fields = ["Number", "Energy", "Gantry", "Collimator", "Couch"]
        for field in required_fields:
            if field not in transformed_beam_data:
                issues.append(f"Missing required field: {field}")
        
        # Validate energy conversion
        if "Energy" in transformed_beam_data:
            energy = transformed_beam_data["Energy"]
            if not isinstance(energy, (int, float)) or energy <= 0:
                issues.append(f"Invalid energy value: {energy}")
        
        # Validate angle ranges
        for angle_field, max_val in [("Gantry", 360), ("Collimator", 360), ("Couch", 360)]:
            if angle_field in transformed_beam_data:
                angle = transformed_beam_data[angle_field]
                if not isinstance(angle, (int, float)) or angle < 0 or angle >= max_val:
                    issues.append(f"Invalid {angle_field} angle: {angle}")
        
        return issues

    @staticmethod
    def validate_dose_grid_transformation(original_dose_data: Dict, transformed_dose_data: Dict) -> List[str]:
        """
        Validate dose grid transformation quality.
        
        Args:
            original_dose_data: Original pinnacle_io dose data
            transformed_dose_data: Transformed dose data
            
        Returns:
            List of validation issues (empty if no issues)
        """
        issues = []
        
        if "DoseGrid" not in transformed_dose_data:
            issues.append("Missing DoseGrid in transformed data")
            return issues
        
        dose_grid = transformed_dose_data["DoseGrid"]
        if not dose_grid:
            return issues  # Valid empty dose grid
        
        # Check required arrays
        required_arrays = ["Spacing", "Dimensions", "Origin"]
        for array_name in required_arrays:
            if array_name not in dose_grid:
                issues.append(f"Missing required array: {array_name}")
            elif not isinstance(dose_grid[array_name], list) or len(dose_grid[array_name]) != 3:
                issues.append(f"{array_name} must be a 3-element list")
        
        # Validate dimensions are positive integers
        if "Dimensions" in dose_grid:
            dims = dose_grid["Dimensions"]
            for i, dim in enumerate(dims):
                if not isinstance(dim, int) or dim <= 0:
                    issues.append(f"Invalid dimension {i}: {dim}")
        
        # Validate spacing values are positive
        if "Spacing" in dose_grid:
            spacing = dose_grid["Spacing"]
            for i, space in enumerate(spacing):
                if not isinstance(space, (int, float)) or space <= 0:
                    issues.append(f"Invalid spacing {i}: {space}")
        
        return issues

    @staticmethod
    def validate_contour_transformation(original_contour_data: Dict, transformed_contour_data: Dict) -> List[str]:
        """
        Validate contour transformation quality.
        
        Args:
            original_contour_data: Original pinnacle_io contour data
            transformed_contour_data: Transformed contour data
            
        Returns:
            List of validation issues (empty if no issues)
        """
        issues = []
        
        if "ContourSequence" not in transformed_contour_data:
            issues.append("Missing ContourSequence in transformed data")
            return issues
        
        contour_sequence = transformed_contour_data["ContourSequence"]
        
        for i, contour in enumerate(contour_sequence):
            # Check required fields
            if "ContourData" not in contour:
                issues.append(f"Contour {i}: Missing ContourData")
                continue
            
            contour_data = contour["ContourData"]
            
            # Validate coordinate data
            if not isinstance(contour_data, list):
                issues.append(f"Contour {i}: ContourData must be a list")
                continue
            
            # Check coordinate count is divisible by 3
            if len(contour_data) % 3 != 0:
                issues.append(f"Contour {i}: Coordinate count {len(contour_data)} not divisible by 3")
            
            # Validate NumberOfContourPoints consistency
            if "NumberOfContourPoints" in contour:
                expected_points = contour["NumberOfContourPoints"]
                actual_points = len(contour_data) // 3
                if expected_points != actual_points:
                    issues.append(f"Contour {i}: Point count mismatch - expected {expected_points}, got {actual_points}")
        
        return issues