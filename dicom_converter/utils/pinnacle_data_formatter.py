"""
Pinnacle Data Formatter for pymedphys compatibility.

This module provides utilities for formatting pinnacle_io model data into the
dictionary format expected by pymedphys DICOM conversion functions.
"""

from typing import Dict, Any, Optional
from datetime import datetime
import logging

from pinnacle_io.models import Patient, Plan, Trial, ImageSet, ROI
from .complex_structure_transformer import ComplexStructureTransformer

logger = logging.getLogger(__name__)


class PinnacleDataFormatter:
    """Utilities for formatting pinnacle_io data for pymedphys compatibility."""

    @staticmethod
    def format_patient_data(patient: Patient) -> Dict[str, Any]:
        """
        Format patient data using built-in to_dict() with additional fields for pymedphys.

        Args:
            patient: Patient model from pinnacle_io

        Returns:
            Dictionary formatted for pymedphys compatibility with proper field names
        """
        # Get base dictionary from pinnacle_io model
        patient_dict = patient.to_dict(include_relationships=False)
        
        # Add pymedphys-required fields that may be missing or need transformation
        formatted_dict = patient_dict.copy()
        
        # Ensure FullName is properly formatted for DICOM (Last^First^Middle^)
        if hasattr(patient, 'dicom_name'):
            formatted_dict["FullName"] = patient.dicom_name
        else:
            # Fallback: construct DICOM name format
            first = patient_dict.get("FirstName", "")
            middle = patient_dict.get("MiddleName", "")  
            last = patient_dict.get("LastName", "")
            formatted_dict["FullName"] = f"{last}^{first}^{middle}^"
        
        # Format date of birth for pymedphys (YYYYMMDD string format)
        if "DateOfBirth" in patient_dict and patient_dict["DateOfBirth"]:
            dob = patient_dict["DateOfBirth"]
            if isinstance(dob, datetime):
                formatted_dict["DOB"] = dob.strftime("%Y%m%d")
            else:
                formatted_dict["DOB"] = str(dob)
        else:
            formatted_dict["DOB"] = ""
        
        # Map MedicalRecordNumber to MRN for pymedphys
        formatted_dict["MRN"] = patient_dict.get("MedicalRecordNumber", "")
        
        return formatted_dict

    @staticmethod
    def format_plan_data(plan: Plan) -> Dict[str, Any]:
        """
        Format plan data using built-in to_dict().

        Args:
            plan: Plan model from pinnacle_io

        Returns:
            Dictionary formatted for pymedphys compatibility
        """
        # pinnacle_io Plan.to_dict() doesn't support include_relationships parameter
        plan_dict = plan.to_dict()
        
        # Add derived fields that pymedphys might expect
        formatted_dict = plan_dict.copy()
        
        # Map name to PlanName for consistency
        if "name" in plan_dict:
            formatted_dict["PlanName"] = plan_dict["name"]
        
        # Add primary CT image set ID if available
        if hasattr(plan, 'primary_ct_image_set_id'):
            formatted_dict["PrimaryCTImageSetID"] = plan.primary_ct_image_set_id
        
        return formatted_dict

    @staticmethod
    def format_trial_data(trial: Trial, include_complex_structures: bool = True) -> Dict[str, Any]:
        """
        Format trial data using built-in to_dict() with complex structure transformation.

        Args:
            trial: Trial model from pinnacle_io
            include_complex_structures: Whether to include transformed beam list and dose grid

        Returns:
            Dictionary formatted for pymedphys compatibility
        """
        trial_dict = trial.to_dict()
        
        # Add derived fields that pymedphys might expect
        formatted_dict = trial_dict.copy()
        
        # Map TrialName for consistency
        if hasattr(trial, 'name'):
            formatted_dict["TrialName"] = trial.name
        elif "trial_name" in trial_dict:
            formatted_dict["TrialName"] = trial_dict["trial_name"]
        
        # Add beam information if available
        if hasattr(trial, 'beam_list') and trial.beam_list:
            formatted_dict["BeamCount"] = len(trial.beam_list)
            formatted_dict["HasBeams"] = True
            
            # Add transformed beam list if requested
            if include_complex_structures:
                try:
                    beam_data = ComplexStructureTransformer.transform_beam_list(trial)
                    formatted_dict.update(beam_data)
                    logger.info(f"Successfully transformed {beam_data['BeamCount']} beams")
                except Exception as e:
                    logger.error(f"Failed to transform beam list: {e}")
                    # Keep basic beam information even if transformation fails
        else:
            formatted_dict["BeamCount"] = 0
            formatted_dict["HasBeams"] = False
            formatted_dict["BeamList"] = []
        
        # Add dose grid information if available
        if hasattr(trial, 'dose_grid') and trial.dose_grid:
            formatted_dict["HasDoseGrid"] = True
            
            # Add transformed dose grid if requested
            if include_complex_structures:
                try:
                    dose_data = ComplexStructureTransformer.transform_dose_grid(trial)
                    formatted_dict.update(dose_data)
                    logger.info("Successfully transformed dose grid")
                except Exception as e:
                    logger.error(f"Failed to transform dose grid: {e}")
                    # Keep basic dose grid information even if transformation fails
        else:
            formatted_dict["HasDoseGrid"] = False
            formatted_dict["DoseGrid"] = None
        
        return formatted_dict

    @staticmethod
    def format_image_set_data(image_set: ImageSet) -> Dict[str, Any]:
        """
        Format image set data using built-in to_dict().

        Args:
            image_set: ImageSet model from pinnacle_io

        Returns:
            Dictionary formatted for pymedphys compatibility
        """
        image_dict = image_set.to_dict()
        
        # Add derived fields that pymedphys might expect
        formatted_dict = image_dict.copy()
        
        # Ensure patient position is properly formatted
        if "PatientPosition" in image_dict:
            formatted_dict["PatientPosition"] = image_dict["PatientPosition"]
        
        # Add image dimensions information
        if hasattr(image_set, 'get_image_dimensions'):
            try:
                dims = image_set.get_image_dimensions()
                formatted_dict["ImageDimensions"] = dims
            except Exception:
                pass  # Skip if method fails
        
        # Add pixel spacing information
        if hasattr(image_set, 'get_pixel_spacing'):
            try:
                spacing = image_set.get_pixel_spacing()
                formatted_dict["PixelSpacing"] = spacing
            except Exception:
                pass  # Skip if method fails
        
        return formatted_dict

    @staticmethod
    def format_roi_data(roi: ROI, include_complex_structures: bool = True) -> Dict[str, Any]:
        """
        Format ROI data using built-in to_dict() with contour transformation.

        Args:
            roi: ROI model from pinnacle_io
            include_complex_structures: Whether to include transformed contour sequence

        Returns:
            Dictionary formatted for pymedphys compatibility
        """
        roi_dict = roi.to_dict()
        
        # Add derived fields that pymedphys might expect
        formatted_dict = roi_dict.copy()
        
        # Map ROI name for consistency
        if "Name" in roi_dict:
            formatted_dict["ROIName"] = roi_dict["Name"]
        
        # Add contour information if available
        if hasattr(roi, 'curve_list') and roi.curve_list:
            formatted_dict["ContourCount"] = len(roi.curve_list)
            formatted_dict["HasContours"] = True
            
            # Add transformed contour sequence if requested
            if include_complex_structures:
                try:
                    contour_data = ComplexStructureTransformer.transform_contour_sequence(roi)
                    formatted_dict.update(contour_data)
                    logger.info(f"Successfully transformed {contour_data['ContourCount']} contours for ROI {roi_dict.get('Name', 'Unknown')}")
                except Exception as e:
                    logger.error(f"Failed to transform contour sequence for ROI {roi_dict.get('Name', 'Unknown')}: {e}")
                    # Keep basic contour information even if transformation fails
        else:
            formatted_dict["ContourCount"] = 0
            formatted_dict["HasContours"] = False
            formatted_dict["ContourSequence"] = []
        
        return formatted_dict

    @staticmethod
    def _format_datetime_for_dicom(dt: Optional[datetime], default: str = "") -> str:
        """
        Format datetime for DICOM compatibility.

        Args:
            dt: datetime object to format
            default: default value if dt is None

        Returns:
            Formatted datetime string (YYYYMMDD) or default
        """
        if dt is None:
            return default
        
        if isinstance(dt, datetime):
            return dt.strftime("%Y%m%d")
        
        return str(dt)

    @staticmethod
    def validate_required_fields(data_dict: Dict[str, Any], required_fields: list[str]) -> list[str]:
        """
        Validate that required fields are present and non-empty.

        Args:
            data_dict: Dictionary to validate
            required_fields: List of required field names

        Returns:
            List of missing or empty required fields
        """
        missing_fields = []
        
        for field in required_fields:
            if field not in data_dict or not data_dict[field]:
                missing_fields.append(field)
        
        return missing_fields

    @classmethod
    def get_patient_required_fields(cls) -> list[str]:
        """Get list of required patient fields for DICOM export."""
        return ["FirstName", "LastName", "MedicalRecordNumber"]

    @classmethod
    def get_plan_required_fields(cls) -> list[str]:
        """Get list of required plan fields for DICOM export."""
        return ["name", "plan_id"]

    @classmethod
    def get_trial_required_fields(cls) -> list[str]:
        """Get list of required trial fields for DICOM export."""
        return ["TrialName", "trial_id"]

    @classmethod
    def get_image_set_required_fields(cls) -> list[str]:
        """Get list of required image set fields for DICOM export."""
        return ["Modality", "PatientPosition", "XDim", "YDim", "ZDim"]

    @classmethod
    def get_roi_required_fields(cls) -> list[str]:
        """Get list of required ROI fields for DICOM export."""
        return ["Name", "ROINumber"]

    @staticmethod
    def format_complete_dataset(
        patient: Patient,
        plan: Plan, 
        trial: Optional[Trial] = None,
        image_set: Optional[ImageSet] = None,
        rois: Optional[list[ROI]] = None,
        include_complex_structures: bool = True,
        validate_data: bool = True
    ) -> Dict[str, Any]:
        """
        Format a complete dataset for pymedphys DICOM export.

        Args:
            patient: Patient model
            plan: Plan model
            trial: Optional trial model
            image_set: Optional image set model
            rois: Optional list of ROI models
            include_complex_structures: Whether to transform complex structures
            validate_data: Whether to validate required fields

        Returns:
            Complete formatted dataset with all components
        """
        result = {
            "patient_info": PinnacleDataFormatter.format_patient_data(patient),
            "plan_info": PinnacleDataFormatter.format_plan_data(plan),
            "validation_errors": [],
            "transformation_status": {
                "beams": False,
                "dose_grid": False,
                "contours": False
            }
        }

        # Add trial data if available
        if trial:
            trial_data = PinnacleDataFormatter.format_trial_data(trial, include_complex_structures)
            result["trial_info"] = trial_data
            
            # Track transformation status
            if "BeamList" in trial_data and trial_data.get("BeamCount", 0) > 0:
                result["transformation_status"]["beams"] = True
            if "DoseGrid" in trial_data and trial_data.get("HasDoseGrid", False):
                result["transformation_status"]["dose_grid"] = True

        # Add image set data if available
        if image_set:
            result["image_info"] = PinnacleDataFormatter.format_image_set_data(image_set)

        # Add ROI data if available
        if rois:
            roi_list = []
            contour_transform_count = 0
            
            for roi in rois:
                roi_data = PinnacleDataFormatter.format_roi_data(roi, include_complex_structures)
                roi_list.append(roi_data)
                
                if "ContourSequence" in roi_data and roi_data.get("ContourCount", 0) > 0:
                    contour_transform_count += 1
            
            result["roi_list"] = roi_list
            result["roi_count"] = len(roi_list)
            
            if contour_transform_count > 0:
                result["transformation_status"]["contours"] = True

        # Perform validation if requested
        if validate_data:
            validation_errors = []
            
            # Validate patient data
            patient_errors = PinnacleDataFormatter.validate_required_fields(
                result["patient_info"], 
                PinnacleDataFormatter.get_patient_required_fields()
            )
            if patient_errors:
                validation_errors.extend([f"Patient: {error}" for error in patient_errors])
            
            # Validate plan data
            plan_errors = PinnacleDataFormatter.validate_required_fields(
                result["plan_info"],
                PinnacleDataFormatter.get_plan_required_fields()
            )
            if plan_errors:
                validation_errors.extend([f"Plan: {error}" for error in plan_errors])
            
            # Validate trial data if present
            if trial and "trial_info" in result:
                trial_errors = PinnacleDataFormatter.validate_required_fields(
                    result["trial_info"],
                    PinnacleDataFormatter.get_trial_required_fields()
                )
                if trial_errors:
                    validation_errors.extend([f"Trial: {error}" for error in trial_errors])
            
            # Validate image set data if present
            if image_set and "image_info" in result:
                image_errors = PinnacleDataFormatter.validate_required_fields(
                    result["image_info"],
                    PinnacleDataFormatter.get_image_set_required_fields()
                )
                if image_errors:
                    validation_errors.extend([f"ImageSet: {error}" for error in image_errors])
            
            # Validate ROI data if present
            if rois and "roi_list" in result:
                for i, roi_data in enumerate(result["roi_list"]):
                    roi_errors = PinnacleDataFormatter.validate_required_fields(
                        roi_data,
                        PinnacleDataFormatter.get_roi_required_fields()
                    )
                    if roi_errors:
                        validation_errors.extend([f"ROI {i}: {error}" for error in roi_errors])
            
            result["validation_errors"] = validation_errors

        return result

    @staticmethod
    def validate_complex_transformations(formatted_data: Dict[str, Any]) -> Dict[str, list[str]]:
        """
        Validate the quality of complex structure transformations.

        Args:
            formatted_data: Result from format_complete_dataset()

        Returns:
            Dictionary with validation results for each transformation type
        """
        validation_results = {
            "beams": [],
            "dose_grid": [],
            "contours": []
        }

        # Validate beam transformations
        if "trial_info" in formatted_data and "BeamList" in formatted_data["trial_info"]:
            beam_list = formatted_data["trial_info"]["BeamList"]
            for i, beam in enumerate(beam_list):
                beam_issues = ComplexStructureTransformer.validate_beam_transformation({}, beam)
                if beam_issues:
                    validation_results["beams"].extend([f"Beam {i}: {issue}" for issue in beam_issues])

        # Validate dose grid transformations
        if "trial_info" in formatted_data and "DoseGrid" in formatted_data["trial_info"]:
            dose_grid = {"DoseGrid": formatted_data["trial_info"]["DoseGrid"]}
            dose_issues = ComplexStructureTransformer.validate_dose_grid_transformation({}, dose_grid)
            validation_results["dose_grid"].extend(dose_issues)

        # Validate contour transformations
        if "roi_list" in formatted_data:
            for i, roi_data in enumerate(formatted_data["roi_list"]):
                if "ContourSequence" in roi_data:
                    contour_data = {"ContourSequence": roi_data["ContourSequence"]}
                    contour_issues = ComplexStructureTransformer.validate_contour_transformation({}, contour_data)
                    if contour_issues:
                        validation_results["contours"].extend([f"ROI {i}: {issue}" for issue in contour_issues])

        return validation_results