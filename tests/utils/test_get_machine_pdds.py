"""
Unit tests for the get_machine_pdds module.

These tests verify the functionality of the get_pdds_from_physics_data method
which extracts photon energy output factors from Pinnacle machine definition files.
"""

import os
from unittest import mock
import pytest
from collections import OrderedDict
import copy

from dicom_converter.utils.get_machine_pdds import get_pdds_from_physics_data, get_pdds_from_plan_machines


@pytest.fixture
def mock_machine_data():
    """
    Create mock machine data that mimics the structure of a Pinnacle machine file.
    """
    machine_data = OrderedDict()
    machine_data["Name"] = "Edge"
    machine_data["MachineType"] = "Other"
    machine_data["VersionTimestamp"] = "2019-02-15 12:23:18"
    machine_data["VersionDescription"] = "For Version 16 institution"
    machine_data["CommissionVersion"] = "Pinnacle v9.10"
    machine_data["DefaultToleranceTableName"] = "20 Photon-Index"
    
    # Create PhotonEnergyList with a single energy
    photon_energy_list = OrderedDict()
    energy = OrderedDict()
    energy["Value"] = 6
    energy["Id"] = 0
    energy["Name"] = "6X"
    energy["ScanPatternLabel"] = ""
    
    # Create PhysicsData with OutputFactor
    physics_data = OrderedDict()
    output_factor = OrderedDict()
    output_factor["ReferenceDepth"] = 10
    output_factor["SourceToCalibrationPointDistance"] = 110
    output_factor["ElectronSSDTolerance"] = 0.1
    output_factor["DosePerMuAtCalibration"] = 0.662
    output_factor["MinMLCPositionAtCalibration"] = 2
    output_factor["CalculatedCalibrationDose"] = 0.018758
    
    physics_data["OutputFactor"] = output_factor
    energy["PhysicsData"] = physics_data
    photon_energy_list["MachineEnergy"] = energy
    machine_data["PhotonEnergyList"] = photon_energy_list
    
    return machine_data


@pytest.fixture
def mock_plan_machines_data():
    """
    Create mock plan machines data that mimics the structure of a Pinnacle plan.Pinnacle.Machines file.
    This contains a dictionary with numbered keys (e.g., "0") for each machine.
    """
    plan_machines = OrderedDict()
    
    # Create a single machine entry
    machine = OrderedDict()
    machine["Name"] = "Edge"
    machine["MachineType"] = "Other"
    machine["VersionTimestamp"] = "2019-02-15 12:23:18"
    machine["VersionDescription"] = "For Version 16 institution"
    machine["CommissionVersion"] = "Pinnacle v9.10"
    machine["DefaultToleranceTableName"] = "20 Photon-Index"
    
    # Create PhotonEnergyList with a single energy
    photon_energy_list = OrderedDict()
    energy = OrderedDict()
    energy["Value"] = 6
    energy["Id"] = 0
    energy["Name"] = "6X"
    energy["ScanPatternLabel"] = ""
    
    # Create PhysicsData with OutputFactor
    physics_data = OrderedDict()
    output_factor = OrderedDict()
    output_factor["ReferenceDepth"] = 10
    output_factor["SourceToCalibrationPointDistance"] = 110
    output_factor["ElectronSSDTolerance"] = 0.1
    output_factor["DosePerMuAtCalibration"] = 0.662
    output_factor["MinMLCPositionAtCalibration"] = 2
    output_factor["CalculatedCalibrationDose"] = 0.018758
    
    physics_data["OutputFactor"] = output_factor
    energy["PhysicsData"] = physics_data
    photon_energy_list["MachineEnergy"] = energy
    machine["PhotonEnergyList"] = photon_energy_list
    
    # Add the machine to the plan machines dictionary with key "0"
    plan_machines["0"] = machine
    
    return plan_machines


class TestGetMachinePdds:
    """Test class for get_machine_pdds module."""
    
    @mock.patch('dicom_converter.utils.get_machine_pdds.PinnacleFileToDict')
    @mock.patch('dicom_converter.utils.get_machine_pdds.glob.glob')
    @mock.patch('dicom_converter.utils.get_machine_pdds.os.path.isdir')
    @mock.patch('dicom_converter.utils.get_machine_pdds.os.listdir')
    def test_get_pdds_from_physics_data_institution_in_path(
            self, mock_listdir, mock_isdir, mock_glob, mock_pinnacle_file_to_dict,
            mock_machine_data):
        """
        Test get_pdds_from_physics_data when Institution_ is in the physics_path.
        """
        # Setup - Use Unix-style paths for cross-platform compatibility
        physics_path = "/path/to/Institution_123/Physics"
        machine_file_path = "/path/to/Institution_123/Physics/ReadOnlyMachineDB/Machine.0/Pinnacle.Machine"
        
        # Mock glob to return a list with one machine file
        mock_glob.return_value = [machine_file_path]
        
        # Mock PinnacleFileToDict to return our mock machine data
        mock_pinnacle_file_to_dict.return_value = mock_machine_data
        
        # Execute
        result = get_pdds_from_physics_data(physics_path)
        
        # Assert
        assert len(result) == 1
        institution, machine_id, machine_file, name, version, energy, dose_cal = result[0]
        
        assert institution == "Institution_123"
        assert machine_id == "Machine.0"  # This comes from the regex match in the function
        assert machine_file == "Pinnacle.Machine"
        assert name == "Edge"
        assert version == "2019-02-15 12:23:18"
        assert energy == "6X"
        assert dose_cal == 0.662
        
        # Verify the mocks were called correctly
        mock_glob.assert_called_once_with(os.path.join(
            physics_path,
            "ReadOnlyMachineDB",
            "Machine.*",
            "Pinnacle.Machine*",
        ))
        mock_pinnacle_file_to_dict.assert_called_once_with(machine_file_path)
        
        # These should not be called when Institution_ is in the path
        mock_listdir.assert_not_called()
        mock_isdir.assert_not_called()
    
    @mock.patch('dicom_converter.utils.get_machine_pdds.PinnacleFileToDict')
    @mock.patch('dicom_converter.utils.get_machine_pdds.glob.glob')
    @mock.patch('dicom_converter.utils.get_machine_pdds.os.path.isdir')
    @mock.patch('dicom_converter.utils.get_machine_pdds.os.listdir')
    def test_get_pdds_from_physics_data_archive_path(
            self, mock_listdir, mock_isdir, mock_glob, mock_pinnacle_file_to_dict,
            mock_machine_data):
        """
        Test get_pdds_from_physics_data when physics_path is at the same level as Institution directory.
        """
        # Setup - Use Unix-style paths for cross-platform compatibility
        physics_path = "/path/to/archive/Physics"
        machine_file_path = "/path/to/archive/Physics/ReadOnlyMachineDB/Machine.0/Pinnacle.Machine"
        
        # Mock glob to return a list with one machine file
        mock_glob.return_value = [machine_file_path]
        
        # Mock PinnacleFileToDict to return our mock machine data
        mock_pinnacle_file_to_dict.return_value = mock_machine_data
        
        # Mock os.listdir to return a list with Institution_456
        mock_listdir.return_value = ["Institution_456", "SomeOtherDir"]
        
        # Mock os.path.isdir to return True for Institution_456
        mock_isdir.side_effect = lambda path: "Institution_456" in path
        
        # Execute
        result = get_pdds_from_physics_data(physics_path)
        
        # Assert
        assert len(result) == 1
        institution, machine_id, machine_file, name, version, energy, dose_cal = result[0]
        
        assert institution == "Institution_456"
        assert machine_id == "Machine.0"
        assert machine_file == "Pinnacle.Machine"
        assert name == "Edge"
        assert version == "2019-02-15 12:23:18"
        assert energy == "6X"
        assert dose_cal == 0.662
        
        # Verify the mocks were called correctly
        mock_glob.assert_called_once_with(os.path.join(
            physics_path,
            "ReadOnlyMachineDB",
            "Machine.*",
            "Pinnacle.Machine*",
        ))
        mock_pinnacle_file_to_dict.assert_called_once_with(machine_file_path)
        mock_listdir.assert_called_once_with(os.path.dirname(physics_path))
        # The function only checks directories until it finds one that matches
        # So it might not check all items in the list
        assert mock_isdir.call_count >= 1
    
    @mock.patch('dicom_converter.utils.get_machine_pdds.PinnacleFileToDict')
    @mock.patch('dicom_converter.utils.get_machine_pdds.glob.glob')
    def test_get_pdds_from_physics_data_multiple_energies(
            self, mock_glob, mock_pinnacle_file_to_dict, mock_machine_data):
        """
        Test get_pdds_from_physics_data with multiple energies in the PhotonEnergyList.
        """
        # Setup - Use Unix-style paths for cross-platform compatibility
        physics_path = "/path/to/Institution_123/Physics"
        machine_file_path = "/path/to/Institution_123/Physics/ReadOnlyMachineDB/Machine.0/Pinnacle.Machine"
        
        # Mock glob to return a list with one machine file
        mock_glob.return_value = [machine_file_path]
        
        # Create a copy of mock_machine_data with an additional energy
        machine_data = mock_machine_data.copy()
        photon_energy_list = OrderedDict()
        
        # First energy (6X)
        energy1 = OrderedDict()
        energy1["Value"] = 6
        energy1["Id"] = 0
        energy1["Name"] = "6X"
        physics_data1 = OrderedDict()
        output_factor1 = OrderedDict()
        output_factor1["DosePerMuAtCalibration"] = 0.662
        physics_data1["OutputFactor"] = output_factor1
        energy1["PhysicsData"] = physics_data1
        
        # Second energy (10X)
        energy2 = OrderedDict()
        energy2["Value"] = 10
        energy2["Id"] = 1
        energy2["Name"] = "10X"
        physics_data2 = OrderedDict()
        output_factor2 = OrderedDict()
        output_factor2["DosePerMuAtCalibration"] = 0.723
        physics_data2["OutputFactor"] = output_factor2
        energy2["PhysicsData"] = physics_data2
        
        # Add both energies to the photon energy list
        photon_energy_list["MachineEnergy_0"] = energy1
        photon_energy_list["MachineEnergy_1"] = energy2
        machine_data["PhotonEnergyList"] = photon_energy_list
        
        # Mock PinnacleFileToDict to return our modified machine data
        mock_pinnacle_file_to_dict.return_value = machine_data
        
        # Execute
        result = get_pdds_from_physics_data(physics_path)
        
        # Assert
        assert len(result) == 2
        
        # Check first energy (6X)
        institution1, machine_id1, machine_file1, name1, version1, energy1, dose_cal1 = result[0]
        assert institution1 == "Institution_123"
        assert machine_id1 == "Machine.0"
        assert name1 == "Edge"
        assert version1 == "2019-02-15 12:23:18"
        assert energy1 == "6X"
        assert dose_cal1 == 0.662
        
        # Check second energy (10X)
        institution2, machine_id2, machine_file2, name2, version2, energy2, dose_cal2 = result[1]
        assert institution2 == "Institution_123"
        assert machine_id2 == "Machine.0"
        assert name2 == "Edge"
        assert version2 == "2019-02-15 12:23:18"
        assert energy2 == "10X"
        assert dose_cal2 == 0.723
    
    @mock.patch('dicom_converter.utils.get_machine_pdds.PinnacleFileToDict')
    @mock.patch('dicom_converter.utils.get_machine_pdds.glob.glob')
    def test_get_pdds_from_physics_data_multiple_machines(
            self, mock_glob, mock_pinnacle_file_to_dict, mock_machine_data):
        """
        Test get_pdds_from_physics_data with multiple machine files.
        """
        # Setup - Use Unix-style paths for cross-platform compatibility
        physics_path = "/path/to/Institution_123/Physics"
        machine_file_path1 = "/path/to/Institution_123/Physics/ReadOnlyMachineDB/Machine.0/Pinnacle.Machine"
        machine_file_path2 = "/path/to/Institution_123/Physics/ReadOnlyMachineDB/Machine.1/Pinnacle.Machine"
        
        # Mock glob to return a list with two machine files
        mock_glob.return_value = [machine_file_path1, machine_file_path2]
        
        # Create two different machine data dictionaries using deep copy
        machine_data1 = copy.deepcopy(mock_machine_data)
        machine_data1["Name"] = "Edge"
        # Ensure first machine has 6X energy
        machine_data1["PhotonEnergyList"]["MachineEnergy"]["Name"] = "6X"
        machine_data1["PhotonEnergyList"]["MachineEnergy"]["PhysicsData"]["OutputFactor"]["DosePerMuAtCalibration"] = 0.662
        
        machine_data2 = copy.deepcopy(mock_machine_data)
        machine_data2["Name"] = "TrueBeam"
        machine_data2["PhotonEnergyList"]["MachineEnergy"]["Name"] = "15X"
        machine_data2["PhotonEnergyList"]["MachineEnergy"]["PhysicsData"]["OutputFactor"]["DosePerMuAtCalibration"] = 0.785
        
        # Mock PinnacleFileToDict to return different data for each machine file
        mock_pinnacle_file_to_dict.side_effect = [machine_data1, machine_data2]
        
        # Execute
        result = get_pdds_from_physics_data(physics_path)
        
        # Assert
        assert len(result) == 2
        
        # Check first machine (Edge with 6X)
        institution1, machine_id1, machine_file1, name1, version1, energy1, dose_cal1 = result[0]
        assert institution1 == "Institution_123"
        assert machine_id1 == "Machine.0"
        assert name1 == "Edge"
        assert version1 == "2019-02-15 12:23:18"
        assert energy1 == "6X"
        assert dose_cal1 == 0.662
        
        # Check second machine (TrueBeam with 15X)
        institution2, machine_id2, machine_file2, name2, version2, energy2, dose_cal2 = result[1]
        assert institution2 == "Institution_123"
        assert machine_id2 == "Machine.1"
        assert name2 == "TrueBeam"
        assert version2 == "2019-02-15 12:23:18"
        assert energy2 == "15X"
        assert dose_cal2 == 0.785
    
    @mock.patch('dicom_converter.utils.get_machine_pdds.glob.glob')
    def test_get_pdds_from_physics_data_no_machines(self, mock_glob):
        """
        Test get_pdds_from_physics_data when no machine files are found.
        """
        # Setup - Use Unix-style paths for cross-platform compatibility
        physics_path = "/path/to/Institution_123/Physics"
        
        # Mock glob to return an empty list
        mock_glob.return_value = []
        
        # Execute
        result = get_pdds_from_physics_data(physics_path)
        
        # Assert
        assert len(result) == 0
        mock_glob.assert_called_once()


class TestGetPddsFromPlanMachines:
    """Test class for get_pdds_from_plan_machines method."""
    
    @mock.patch('dicom_converter.utils.get_machine_pdds.PinnacleFileToDict')
    @mock.patch('dicom_converter.utils.get_machine_pdds.os.path.join')
    def test_get_pdds_from_plan_machines_basic(self, mock_join, mock_pinnacle_file_to_dict, mock_plan_machines_data):
        """
        Test get_pdds_from_plan_machines with a basic plan path containing Institution_.
        """
        # Setup - Use Unix-style paths for cross-platform compatibility
        plan_path = "/path/to/Institution_123/Plan"
        machine_file_path = "/path/to/Institution_123/Plan/plan.Pinnacle.Machines"
        
        # Mock os.path.join to return the machine file path
        mock_join.return_value = machine_file_path
        
        # Mock PinnacleFileToDict to return our mock plan machines data
        mock_pinnacle_file_to_dict.return_value = mock_plan_machines_data
        
        # Execute
        result = get_pdds_from_plan_machines(plan_path)
        
        # Assert
        assert len(result) == 1
        institution, machine_id, machine_file, name, version, energy, dose_cal = result[0]
        
        assert institution == "Institution_123"
        assert machine_id == "Machine.0"
        assert machine_file == "plan.Pinnacle.Machines"
        assert name == "Edge"
        assert version == "2019-02-15 12:23:18"
        assert energy == "6X"
        assert dose_cal == 0.662
        
        # Verify the mocks were called correctly
        mock_join.assert_called_once_with(plan_path, "plan.Pinnacle.Machines")
        mock_pinnacle_file_to_dict.assert_called_once_with(machine_file_path)
    
    @mock.patch('dicom_converter.utils.get_machine_pdds.PinnacleFileToDict')
    @mock.patch('dicom_converter.utils.get_machine_pdds.os.path.join')
    def test_get_pdds_from_plan_machines_multiple_machines(self, mock_join, mock_pinnacle_file_to_dict):
        """
        Test get_pdds_from_plan_machines with multiple machines in the plan.
        """
        # Setup - Use Unix-style paths for cross-platform compatibility
        plan_path = "/path/to/Institution_456/Plan"
        machine_file_path = "/path/to/Institution_456/Plan/plan.Pinnacle.Machines"
        
        # Mock os.path.join to return the machine file path
        mock_join.return_value = machine_file_path
        
        # Create plan machines data with multiple machines
        plan_machines = OrderedDict()
        
        # First machine (Edge with 6X)
        machine1 = OrderedDict()
        machine1["Name"] = "Edge"
        machine1["VersionTimestamp"] = "2019-02-15 12:23:18"
        photon_energy_list1 = OrderedDict()
        energy1 = OrderedDict()
        energy1["Name"] = "6X"
        physics_data1 = OrderedDict()
        output_factor1 = OrderedDict()
        output_factor1["DosePerMuAtCalibration"] = 0.662
        physics_data1["OutputFactor"] = output_factor1
        energy1["PhysicsData"] = physics_data1
        photon_energy_list1["MachineEnergy"] = energy1
        machine1["PhotonEnergyList"] = photon_energy_list1
        
        # Second machine (TrueBeam with 15X)
        machine2 = OrderedDict()
        machine2["Name"] = "TrueBeam"
        machine2["VersionTimestamp"] = "2020-01-10 09:45:30"
        photon_energy_list2 = OrderedDict()
        energy2 = OrderedDict()
        energy2["Name"] = "15X"
        physics_data2 = OrderedDict()
        output_factor2 = OrderedDict()
        output_factor2["DosePerMuAtCalibration"] = 0.785
        physics_data2["OutputFactor"] = output_factor2
        energy2["PhysicsData"] = physics_data2
        photon_energy_list2["MachineEnergy"] = energy2
        machine2["PhotonEnergyList"] = photon_energy_list2
        
        # Add machines to the plan machines dictionary
        plan_machines["0"] = machine1
        plan_machines["1"] = machine2
        
        # Mock PinnacleFileToDict to return our plan machines data
        mock_pinnacle_file_to_dict.return_value = plan_machines
        
        # Execute
        result = get_pdds_from_plan_machines(plan_path)
        
        # Assert
        assert len(result) == 2
        
        # Check first machine (Edge with 6X)
        institution1, machine_id1, machine_file1, name1, version1, energy1, dose_cal1 = result[0]
        assert institution1 == "Institution_456"
        assert machine_id1 == "Machine.0"
        assert machine_file1 == "plan.Pinnacle.Machines"
        assert name1 == "Edge"
        assert version1 == "2019-02-15 12:23:18"
        assert energy1 == "6X"
        assert dose_cal1 == 0.662
        
        # Check second machine (TrueBeam with 15X)
        institution2, machine_id2, machine_file2, name2, version2, energy2, dose_cal2 = result[1]
        assert institution2 == "Institution_456"
        assert machine_id2 == "Machine.1"
        assert machine_file2 == "plan.Pinnacle.Machines"
        assert name2 == "TrueBeam"
        assert version2 == "2020-01-10 09:45:30"
        assert energy2 == "15X"
        assert dose_cal2 == 0.785
    
    @mock.patch('dicom_converter.utils.get_machine_pdds.PinnacleFileToDict')
    @mock.patch('dicom_converter.utils.get_machine_pdds.os.path.join')
    def test_get_pdds_from_plan_machines_multiple_energies(self, mock_join, mock_pinnacle_file_to_dict):
        """
        Test get_pdds_from_plan_machines with multiple energies for a single machine.
        """
        # Setup - Use Unix-style paths for cross-platform compatibility
        plan_path = "/path/to/Institution_789/Plan"
        machine_file_path = "/path/to/Institution_789/Plan/plan.Pinnacle.Machines"
        
        # Mock os.path.join to return the machine file path
        mock_join.return_value = machine_file_path
        
        # Create plan machines data with a machine that has multiple energies
        plan_machines = OrderedDict()
        machine = OrderedDict()
        machine["Name"] = "Edge"
        machine["VersionTimestamp"] = "2019-02-15 12:23:18"
        
        # Create PhotonEnergyList with multiple energies
        photon_energy_list = OrderedDict()
        
        # First energy (6X)
        energy1 = OrderedDict()
        energy1["Name"] = "6X"
        physics_data1 = OrderedDict()
        output_factor1 = OrderedDict()
        output_factor1["DosePerMuAtCalibration"] = 0.662
        physics_data1["OutputFactor"] = output_factor1
        energy1["PhysicsData"] = physics_data1
        
        # Second energy (10X)
        energy2 = OrderedDict()
        energy2["Name"] = "10X"
        physics_data2 = OrderedDict()
        output_factor2 = OrderedDict()
        output_factor2["DosePerMuAtCalibration"] = 0.723
        physics_data2["OutputFactor"] = output_factor2
        energy2["PhysicsData"] = physics_data2
        
        # Add energies to the photon energy list
        photon_energy_list["MachineEnergy_0"] = energy1
        photon_energy_list["MachineEnergy_1"] = energy2
        
        # Add photon energy list to the machine
        machine["PhotonEnergyList"] = photon_energy_list
        
        # Add machine to the plan machines dictionary
        plan_machines["0"] = machine
        
        # Mock PinnacleFileToDict to return our plan machines data
        mock_pinnacle_file_to_dict.return_value = plan_machines
        
        # Execute
        result = get_pdds_from_plan_machines(plan_path)
        
        # Assert
        assert len(result) == 2
        
        # Check first energy (6X)
        institution1, machine_id1, machine_file1, name1, version1, energy1, dose_cal1 = result[0]
        assert institution1 == "Institution_789"
        assert machine_id1 == "Machine.0"
        assert machine_file1 == "plan.Pinnacle.Machines"
        assert name1 == "Edge"
        assert version1 == "2019-02-15 12:23:18"
        assert energy1 == "6X"
        assert dose_cal1 == 0.662
        
        # Check second energy (10X)
        institution2, machine_id2, machine_file2, name2, version2, energy2, dose_cal2 = result[1]
        assert institution2 == "Institution_789"
        assert machine_id2 == "Machine.0"
        assert machine_file2 == "plan.Pinnacle.Machines"
        assert name2 == "Edge"
        assert version2 == "2019-02-15 12:23:18"
        assert energy2 == "10X"
        assert dose_cal2 == 0.723
    
    @mock.patch('dicom_converter.utils.get_machine_pdds.re.search')
    def test_get_pdds_from_plan_machines_no_institution_match(self, mock_search):
        """
        Test get_pdds_from_plan_machines when no Institution_ match is found in the path.
        """
        # Setup - Use Unix-style paths for cross-platform compatibility
        plan_path = "/path/to/Plan"
        
        # Mock re.search to return None (no match)
        mock_search.return_value = None
        
        # Execute and Assert
        with pytest.raises(AttributeError):
            get_pdds_from_plan_machines(plan_path)
