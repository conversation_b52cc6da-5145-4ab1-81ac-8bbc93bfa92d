"""
Test suite for PinnacleDataFormatter.

Tests the data formatting utility against real pinnacle_io models.
"""

import sys
from pathlib import Path

# Add parent directories to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from pinnacle_io.api import PinnacleReader
import pytest
from dicom_converter.utils.pinnacle_data_formatter import PinnacleDataFormatter


@pytest.fixture
def test_archive_path():
    """Path to test archive."""
    return Path(__file__).parent.parent / "test_data" / "archive_01"


@pytest.fixture
def pinnacle_data(test_archive_path):
    """Load test pinnacle data."""
    if not test_archive_path.exists():
        pytest.skip(f"Test data not found at {test_archive_path}")
    
    reader = PinnacleReader(test_archive_path)
    institution = reader.get_institution()
    patient_lite = institution.patient_lite_list[0]
    patient = reader.get_patient(institution=institution, patient=patient_lite)
    plan = patient.plan_list[0]
    trials = reader.get_trials(institution, patient, plan)
    trial = trials[0] if trials else None
    
    # Load image set if available
    image_set = None
    if hasattr(plan, 'primary_ct_image_set_id') and plan.primary_ct_image_set_id is not None:
        try:
            image_set = reader.get_image_set(institution, patient, plan.primary_ct_image_set_id)
        except Exception:
            pass
    
    # Load ROIs if available
    rois = []
    try:
        rois = reader.get_rois(institution, patient, plan)
    except Exception:
        pass
    
    return {
        'patient': patient,
        'plan': plan,
        'trial': trial,
        'image_set': image_set,
        'rois': rois
    }


def test_format_patient_data(pinnacle_data):
    """Test patient data formatting."""
    patient = pinnacle_data['patient']
    formatter = PinnacleDataFormatter()
    
    # Test formatting
    formatted_data = formatter.format_patient_data(patient)
    
    # Verify required fields are present
    required_fields = formatter.get_patient_required_fields()
    missing_fields = formatter.validate_required_fields(formatted_data, required_fields)
    
    # Should have minimal missing fields for test data
    print(f"Patient formatted data keys: {list(formatted_data.keys())}")
    print(f"Missing required fields: {missing_fields}")
    
    # Check specific transformations
    assert "FullName" in formatted_data, "FullName should be added"
    assert "DOB" in formatted_data, "DOB should be formatted"
    assert "MRN" in formatted_data, "MRN should be mapped from MedicalRecordNumber"
    
    # Verify DICOM name format (Last^First^Middle^)
    full_name = formatted_data["FullName"]
    assert "^" in full_name, f"FullName should be in DICOM format, got: {full_name}"
    
    # Verify DOB format (should be YYYYMMDD string)
    dob = formatted_data["DOB"]
    if dob:  # May be empty for test data
        assert isinstance(dob, str), f"DOB should be string, got: {type(dob)}"
        if len(dob) == 8 and dob.isdigit():
            # Valid YYYYMMDD format
            pass
        else:
            print(f"Warning: DOB format may not be YYYYMMDD: {dob}")


def test_format_plan_data(pinnacle_data):
    """Test plan data formatting."""
    plan = pinnacle_data['plan']
    formatter = PinnacleDataFormatter()
    
    # Test formatting
    formatted_data = formatter.format_plan_data(plan)
    
    # Verify required fields are present
    required_fields = formatter.get_plan_required_fields()
    missing_fields = formatter.validate_required_fields(formatted_data, required_fields)
    
    print(f"Plan formatted data keys: {list(formatted_data.keys())}")
    print(f"Missing required fields: {missing_fields}")
    
    # Check specific transformations
    if "name" in formatted_data:
        assert "PlanName" in formatted_data, "PlanName should be mapped from name"
        assert formatted_data["PlanName"] == formatted_data["name"]
    
    # Check for primary CT image set ID
    if hasattr(plan, 'primary_ct_image_set_id'):
        assert "PrimaryCTImageSetID" in formatted_data


def test_format_trial_data(pinnacle_data):
    """Test trial data formatting."""
    trial = pinnacle_data['trial']
    if trial is None:
        pytest.skip("No trial data available in test dataset")
    
    formatter = PinnacleDataFormatter()
    
    # Test formatting without complex structures
    formatted_data = formatter.format_trial_data(trial, include_complex_structures=False)
    
    # Verify required fields are present
    required_fields = formatter.get_trial_required_fields()
    missing_fields = formatter.validate_required_fields(formatted_data, required_fields)
    
    print(f"Trial formatted data keys: {list(formatted_data.keys())}")
    print(f"Missing required fields: {missing_fields}")
    
    # Check specific transformations
    assert "TrialName" in formatted_data, "TrialName should be added"
    assert "BeamCount" in formatted_data, "BeamCount should be added"
    assert "HasBeams" in formatted_data, "HasBeams should be added"
    assert "HasDoseGrid" in formatted_data, "HasDoseGrid should be added"
    
    # Verify beam information
    beam_count = formatted_data["BeamCount"]
    has_beams = formatted_data["HasBeams"]
    assert isinstance(beam_count, int), "BeamCount should be integer"
    assert isinstance(has_beams, bool), "HasBeams should be boolean"
    assert (beam_count > 0) == has_beams, "BeamCount and HasBeams should be consistent"


def test_format_trial_data_with_complex_structures(pinnacle_data):
    """Test trial data formatting with complex structure transformation."""
    trial = pinnacle_data['trial']
    if trial is None:
        pytest.skip("No trial data available in test dataset")
    
    formatter = PinnacleDataFormatter()
    
    # Test formatting with complex structures
    formatted_data = formatter.format_trial_data(trial, include_complex_structures=True)
    
    print(f"Trial with complex structures keys: {list(formatted_data.keys())}")
    
    # Check for transformed beam list if beams are present
    if formatted_data.get("HasBeams", False):
        assert "BeamList" in formatted_data, "BeamList should be included with complex structures"
        beam_list = formatted_data["BeamList"]
        assert isinstance(beam_list, list), "BeamList should be a list"
        
        # Verify beam transformation if any beams exist
        if beam_list:
            for i, beam in enumerate(beam_list):
                assert "Number" in beam, f"Beam {i} should have Number field"
                assert "Energy" in beam, f"Beam {i} should have Energy field"
                assert "Gantry" in beam, f"Beam {i} should have Gantry field"
    
    # Check for transformed dose grid if dose is present
    if formatted_data.get("HasDoseGrid", False):
        assert "DoseGrid" in formatted_data, "DoseGrid should be included with complex structures"
        dose_grid = formatted_data["DoseGrid"]
        
        if dose_grid is not None:
            assert isinstance(dose_grid, dict), "DoseGrid should be a dictionary"
            # Check for consolidated arrays
            for array_name in ["Spacing", "Dimensions", "Origin"]:
                if array_name in dose_grid:
                    assert isinstance(dose_grid[array_name], list), f"{array_name} should be a list"
                    assert len(dose_grid[array_name]) == 3, f"{array_name} should have 3 elements"


def test_format_image_set_data(pinnacle_data):
    """Test image set data formatting."""
    image_set = pinnacle_data['image_set']
    if image_set is None:
        pytest.skip("No image set data available in test dataset")
    
    formatter = PinnacleDataFormatter()
    
    # Test formatting
    formatted_data = formatter.format_image_set_data(image_set)
    
    # Verify required fields are present
    required_fields = formatter.get_image_set_required_fields()
    missing_fields = formatter.validate_required_fields(formatted_data, required_fields)
    
    print(f"ImageSet formatted data keys: {list(formatted_data.keys())}")
    print(f"Missing required fields: {missing_fields}")
    
    # Check that dimensions are present
    assert "XDim" in formatted_data, "XDim should be present"
    assert "YDim" in formatted_data, "YDim should be present"
    assert "ZDim" in formatted_data, "ZDim should be present"
    
    # Verify patient position
    assert "PatientPosition" in formatted_data, "PatientPosition should be present"


def test_format_roi_data(pinnacle_data):
    """Test ROI data formatting."""
    rois = pinnacle_data['rois']
    if not rois:
        pytest.skip("No ROI data available in test dataset")
    
    formatter = PinnacleDataFormatter()
    roi = rois[0]  # Test first ROI
    
    # Test formatting without complex structures
    formatted_data = formatter.format_roi_data(roi, include_complex_structures=False)
    
    # Verify required fields are present
    required_fields = formatter.get_roi_required_fields()
    missing_fields = formatter.validate_required_fields(formatted_data, required_fields)
    
    print(f"ROI formatted data keys: {list(formatted_data.keys())}")
    print(f"Missing required fields: {missing_fields}")
    
    # Check specific transformations
    if "Name" in formatted_data:
        assert "ROIName" in formatted_data, "ROIName should be mapped from Name"
        assert formatted_data["ROIName"] == formatted_data["Name"]
    
    # Check contour information
    assert "ContourCount" in formatted_data, "ContourCount should be added"
    assert "HasContours" in formatted_data, "HasContours should be added"
    
    # Verify contour information
    contour_count = formatted_data["ContourCount"]
    has_contours = formatted_data["HasContours"]
    assert isinstance(contour_count, int), "ContourCount should be integer"
    assert isinstance(has_contours, bool), "HasContours should be boolean"


def test_format_roi_data_with_complex_structures(pinnacle_data):
    """Test ROI data formatting with complex structure transformation."""
    rois = pinnacle_data['rois']
    if not rois:
        pytest.skip("No ROI data available in test dataset")
    
    formatter = PinnacleDataFormatter()
    
    # Find ROI with contours for testing
    roi_with_contours = None
    for roi in rois:
        if hasattr(roi, 'curve_list') and roi.curve_list:
            roi_with_contours = roi
            break
    
    if roi_with_contours is None:
        pytest.skip("No ROI with contour data available in test dataset")
    
    # Test formatting with complex structures
    formatted_data = formatter.format_roi_data(roi_with_contours, include_complex_structures=True)
    
    print(f"ROI with complex structures keys: {list(formatted_data.keys())}")
    
    # Check for transformed contour sequence if contours are present
    if formatted_data.get("HasContours", False):
        assert "ContourSequence" in formatted_data, "ContourSequence should be included with complex structures"
        contour_sequence = formatted_data["ContourSequence"]
        assert isinstance(contour_sequence, list), "ContourSequence should be a list"
        
        # Verify contour transformation if any contours exist
        if contour_sequence:
            for i, contour in enumerate(contour_sequence):
                assert "ContourData" in contour, f"Contour {i} should have ContourData field"
                assert "NumberOfContourPoints" in contour, f"Contour {i} should have NumberOfContourPoints field"
                
                # Verify coordinate data consistency
                coords = contour["ContourData"]
                if coords:  # May be empty if transformation failed
                    assert isinstance(coords, list), f"Contour {i} ContourData should be a list"
                    assert len(coords) % 3 == 0, f"Contour {i} coordinates must be divisible by 3"


def test_field_validation():
    """Test field validation utility."""
    formatter = PinnacleDataFormatter()
    
    # Test with complete data
    complete_data = {
        "FirstName": "John",
        "LastName": "Doe", 
        "MedicalRecordNumber": "12345"
    }
    
    required_fields = ["FirstName", "LastName", "MedicalRecordNumber"]
    missing = formatter.validate_required_fields(complete_data, required_fields)
    assert missing == [], f"Should have no missing fields, got: {missing}"
    
    # Test with missing data
    incomplete_data = {
        "FirstName": "John",
        "MedicalRecordNumber": ""  # Empty field
    }
    
    missing = formatter.validate_required_fields(incomplete_data, required_fields)
    assert "LastName" in missing, "Should detect missing LastName"
    assert "MedicalRecordNumber" in missing, "Should detect empty MedicalRecordNumber"


def test_format_complete_dataset(pinnacle_data):
    """Test complete dataset formatting functionality."""
    patient = pinnacle_data['patient']
    plan = pinnacle_data['plan']
    trial = pinnacle_data['trial']
    image_set = pinnacle_data['image_set'] 
    rois = pinnacle_data['rois']
    
    formatter = PinnacleDataFormatter()
    
    # Test complete dataset formatting
    result = formatter.format_complete_dataset(
        patient=patient,
        plan=plan,
        trial=trial,
        image_set=image_set,
        rois=rois,
        include_complex_structures=True,
        validate_data=True
    )
    
    # Verify basic structure
    assert "patient_info" in result
    assert "plan_info" in result
    assert "validation_errors" in result
    assert "transformation_status" in result
    
    # Verify transformation status tracking
    transformation_status = result["transformation_status"]
    assert "beams" in transformation_status
    assert "dose_grid" in transformation_status
    assert "contours" in transformation_status
    
    # Verify optional components are included when present
    if trial:
        assert "trial_info" in result
    if image_set:
        assert "image_info" in result
    if rois:
        assert "roi_list" in result
        assert "roi_count" in result
        assert result["roi_count"] == len(rois)
    
    print(f"Complete dataset keys: {list(result.keys())}")
    print(f"Transformation status: {result['transformation_status']}")
    print(f"Validation errors: {result['validation_errors']}")


def test_validate_complex_transformations(pinnacle_data):
    """Test complex transformation validation functionality."""
    patient = pinnacle_data['patient']
    plan = pinnacle_data['plan']
    trial = pinnacle_data['trial']
    rois = pinnacle_data['rois']
    
    formatter = PinnacleDataFormatter()
    
    # Format complete dataset with complex structures
    formatted_data = formatter.format_complete_dataset(
        patient=patient,
        plan=plan,
        trial=trial,
        rois=rois,
        include_complex_structures=True,
        validate_data=False  # Skip basic validation to focus on complex validation
    )
    
    # Validate complex transformations
    validation_results = formatter.validate_complex_transformations(formatted_data)
    
    # Verify validation structure
    assert "beams" in validation_results
    assert "dose_grid" in validation_results
    assert "contours" in validation_results
    
    # Each should be a list of validation issues
    assert isinstance(validation_results["beams"], list)
    assert isinstance(validation_results["dose_grid"], list)
    assert isinstance(validation_results["contours"], list)
    
    print(f"Complex transformation validation results:")
    for transform_type, issues in validation_results.items():
        print(f"  {transform_type}: {len(issues)} issues")
        for issue in issues[:3]:  # Show first 3 issues
            print(f"    - {issue}")


def test_format_complete_dataset_minimal():
    """Test complete dataset formatting with minimal required data."""
    from unittest.mock import Mock
    
    # Create minimal mock data
    patient = Mock()
    patient.to_dict.return_value = {
        "FirstName": "Test",
        "LastName": "Patient",
        "MedicalRecordNumber": "12345"
    }
    patient.dicom_name = "Patient^Test^"
    patient.date_of_birth = None
    
    plan = Mock()
    plan.to_dict.return_value = {
        "name": "Test Plan",
        "plan_id": 1
    }
    
    formatter = PinnacleDataFormatter()
    
    # Test with minimal data (no trial, image_set, or rois)
    result = formatter.format_complete_dataset(
        patient=patient,
        plan=plan,
        trial=None,
        image_set=None,
        rois=None,
        include_complex_structures=True,
        validate_data=True
    )
    
    # Verify basic structure is still present
    assert "patient_info" in result
    assert "plan_info" in result
    assert "validation_errors" in result
    assert "transformation_status" in result
    
    # Verify transformation status shows no transformations
    transformation_status = result["transformation_status"]
    assert transformation_status["beams"] is False
    assert transformation_status["dose_grid"] is False
    assert transformation_status["contours"] is False
    
    # Should have minimal or no validation errors
    validation_errors = result["validation_errors"]
    print(f"Minimal dataset validation errors: {validation_errors}")
    
    # Patient and plan should be valid
    patient_errors = [e for e in validation_errors if e.startswith("Patient:")]
    plan_errors = [e for e in validation_errors if e.startswith("Plan:")]
    
    assert len(patient_errors) == 0, f"Patient should be valid: {patient_errors}"
    assert len(plan_errors) == 0, f"Plan should be valid: {plan_errors}"


def load_test_data(test_archive_path):
    """Helper function to load test data without pytest fixture."""
    if not test_archive_path.exists():
        raise FileNotFoundError(f"Test data not found at {test_archive_path}")
    
    reader = PinnacleReader(test_archive_path)
    institution = reader.get_institution()
    patient_lite = institution.patient_lite_list[0]
    patient = reader.get_patient(institution=institution, patient=patient_lite)
    plan = patient.plan_list[0]
    trials = reader.get_trials(institution, patient, plan)
    trial = trials[0] if trials else None
    
    # Load image set if available
    image_set = None
    if hasattr(plan, 'primary_ct_image_set_id') and plan.primary_ct_image_set_id is not None:
        try:
            image_set = reader.get_image_set(institution, patient, plan.primary_ct_image_set_id)
        except Exception:
            pass
    
    # Load ROIs if available
    rois = []
    try:
        rois = reader.get_rois(institution, patient, plan)
    except Exception:
        pass
    
    return {
        'patient': patient,
        'plan': plan,
        'trial': trial,
        'image_set': image_set,
        'rois': rois
    }


if __name__ == "__main__":
    """Run tests manually for development."""
    test_archive = Path(__file__).parent.parent / "test_data" / "archive_01"
    
    if test_archive.exists():
        data = load_test_data(test_archive)
        
        print("Testing PinnacleDataFormatter...")
        test_format_patient_data(data)
        test_format_plan_data(data)
        test_format_trial_data(data)
        test_format_trial_data_with_complex_structures(data)
        test_format_image_set_data(data)
        test_format_roi_data(data)
        test_format_roi_data_with_complex_structures(data)
        test_field_validation()
        test_format_complete_dataset(data)
        test_validate_complex_transformations(data)
        test_format_complete_dataset_minimal()
        print("All tests completed!")
    else:
        print(f"Test archive not found at: {test_archive}")
        print("Please ensure test data is available before running formatter tests.")