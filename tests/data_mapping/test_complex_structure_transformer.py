"""
Test suite for ComplexStructureTransformer.

Tests the complex structure transformation utilities against real pinnacle_io models.
"""

import sys
import struct
from pathlib import Path
from unittest.mock import Mock, MagicMock

# Add parent directories to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from pinnacle_io.api import PinnacleReader
import pytest
from dicom_converter.utils.complex_structure_transformer import ComplexStructureTransformer


@pytest.fixture
def test_archive_path():
    """Path to test archive."""
    return Path(__file__).parent.parent / "test_data" / "archive_01"


@pytest.fixture
def pinnacle_data(test_archive_path):
    """Load test pinnacle data."""
    if not test_archive_path.exists():
        pytest.skip(f"Test data not found at {test_archive_path}")
    
    reader = PinnacleReader(test_archive_path)
    institution = reader.get_institution()
    patient_lite = institution.patient_lite_list[0]
    patient = reader.get_patient(institution=institution, patient=patient_lite)
    plan = patient.plan_list[0]
    trials = reader.get_trials(institution, patient, plan)
    trial = trials[0] if trials else None
    
    # Load ROIs if available
    rois = []
    try:
        rois = reader.get_rois(institution, patient, plan)
    except Exception:
        pass
    
    return {
        'patient': patient,
        'plan': plan,
        'trial': trial,
        'rois': rois
    }


def create_mock_beam():
    """Create a mock beam for testing."""
    beam = Mock()
    beam.to_dict.return_value = {
        "BeamNumber": 1,
        "Name": "Test Beam",
        "MachineEnergyName": "6X",
        "GantryAngle": 0.0,
        "CollimatorAngle": 0.0,
        "CouchAngle": 0.0,
        "IsocenterX": 0.0,
        "IsocenterY": 0.0,
        "IsocenterZ": 0.0,
        "DoseRate": 600,
        "MonitorUnits": 100.0,
        "BeamType": "STATIC",
        "Modality": "PHOTON"
    }
    return beam


def create_mock_dose_grid():
    """Create a mock dose grid for testing."""
    dose_grid = Mock()
    dose_grid.to_dict.return_value = {
        "VoxelSizeX": 2.5,
        "VoxelSizeY": 2.5,
        "VoxelSizeZ": 2.5,
        "DimensionX": 100,
        "DimensionY": 100,
        "DimensionZ": 50,
        "OriginX": -125.0,
        "OriginY": -125.0,
        "OriginZ": -62.5,
        "DoseUnits": "GY",
        "DoseGridScaling": 1.0,
        "DoseType": "PHYSICAL"
    }
    dose_grid.get_dose_data = Mock(return_value=True)
    return dose_grid


def create_mock_roi_with_contours():
    """Create a mock ROI with contour data for testing."""
    roi = Mock()
    roi.to_dict.return_value = {
        "Name": "Test ROI",
        "ROINumber": 1
    }
    
    # Create mock curve with binary points data
    curve = Mock()
    
    # Create sample contour points (triangle: 3 points * 3 coordinates = 9 floats)
    test_points = [
        0.0, 0.0, 0.0,    # Point 1: (0, 0, 0)
        10.0, 0.0, 0.0,   # Point 2: (10, 0, 0)
        5.0, 10.0, 0.0    # Point 3: (5, 10, 0)
    ]
    
    # Pack as binary data (little-endian float32)
    binary_data = struct.pack('<9f', *test_points)
    
    curve.to_dict.return_value = {
        "PointsData": binary_data,
        "GeometricType": "CLOSED_PLANAR",
        "SliceThickness": 2.5,
        "SliceLocation": 0.0,
        "CurveID": 1
    }
    
    roi.curve_list = [curve]
    return roi


def test_transform_beam_list_with_mock_data():
    """Test beam list transformation with mock data."""
    # Create mock trial with beam list
    trial = Mock()
    trial.beam_list = [create_mock_beam()]
    
    # Test transformation
    result = ComplexStructureTransformer.transform_beam_list(trial)
    
    # Verify structure
    assert "BeamList" in result
    assert "BeamCount" in result
    assert result["BeamCount"] == 1
    assert len(result["BeamList"]) == 1
    
    # Verify beam transformation
    beam = result["BeamList"][0]
    assert beam["Number"] == 1
    assert beam["Name"] == "Test Beam"
    assert beam["Energy"] == 6.0  # Parsed from "6X"
    assert beam["Gantry"] == 0.0
    assert beam["Collimator"] == 0.0
    assert beam["Couch"] == 0.0
    assert beam["Isocenter"] == [0.0, 0.0, 0.0]
    assert beam["DoseRate"] == 600
    assert beam["MonitorUnits"] == 100.0


def test_transform_beam_list_with_real_data(pinnacle_data):
    """Test beam list transformation with real pinnacle_io data."""
    trial = pinnacle_data['trial']
    if trial is None or not hasattr(trial, 'beam_list') or not trial.beam_list:
        pytest.skip("No beam data available in test dataset")
    
    # Test transformation
    result = ComplexStructureTransformer.transform_beam_list(trial)
    
    # Verify basic structure
    assert "BeamList" in result
    assert "BeamCount" in result
    assert result["BeamCount"] == len(trial.beam_list)
    assert len(result["BeamList"]) == result["BeamCount"]
    
    # Verify each beam has required fields
    for beam in result["BeamList"]:
        assert "Number" in beam
        assert "Energy" in beam
        assert "Gantry" in beam
        assert "Collimator" in beam
        assert "Couch" in beam
        assert "Isocenter" in beam
        assert isinstance(beam["Isocenter"], list)
        assert len(beam["Isocenter"]) == 3


def test_transform_dose_grid_with_mock_data():
    """Test dose grid transformation with mock data."""
    # Create mock trial with dose grid
    trial = Mock()
    trial.dose_grid = create_mock_dose_grid()
    
    # Test transformation
    result = ComplexStructureTransformer.transform_dose_grid(trial)
    
    # Verify structure
    assert "DoseGrid" in result
    assert "HasDoseGrid" in result
    assert result["HasDoseGrid"] is True
    
    dose_grid = result["DoseGrid"]
    assert dose_grid is not None
    
    # Verify consolidated arrays
    assert "Spacing" in dose_grid
    assert dose_grid["Spacing"] == [2.5, 2.5, 2.5]
    
    assert "Dimensions" in dose_grid
    assert dose_grid["Dimensions"] == [100, 100, 50]
    
    assert "Origin" in dose_grid
    assert dose_grid["Origin"] == [-125.0, -125.0, -62.5]
    
    # Verify other fields
    assert dose_grid["DoseUnits"] == "GY"
    assert dose_grid["DoseGridScaling"] == 1.0
    assert dose_grid["GridType"] == "REGULAR"


def test_transform_dose_grid_with_real_data(pinnacle_data):
    """Test dose grid transformation with real pinnacle_io data."""
    trial = pinnacle_data['trial']
    if trial is None or not hasattr(trial, 'dose_grid') or not trial.dose_grid:
        pytest.skip("No dose grid data available in test dataset")
    
    # Test transformation
    result = ComplexStructureTransformer.transform_dose_grid(trial)
    
    # Verify basic structure
    assert "DoseGrid" in result
    assert "HasDoseGrid" in result
    
    if result["HasDoseGrid"]:
        dose_grid = result["DoseGrid"]
        assert dose_grid is not None
        
        # Verify required arrays are present and correctly sized
        assert "Spacing" in dose_grid
        assert isinstance(dose_grid["Spacing"], list)
        assert len(dose_grid["Spacing"]) == 3
        
        assert "Dimensions" in dose_grid
        assert isinstance(dose_grid["Dimensions"], list)
        assert len(dose_grid["Dimensions"]) == 3
        
        assert "Origin" in dose_grid
        assert isinstance(dose_grid["Origin"], list)
        assert len(dose_grid["Origin"]) == 3


def test_transform_contour_sequence_with_mock_data():
    """Test contour sequence transformation with mock data."""
    roi = create_mock_roi_with_contours()
    
    # Test transformation
    result = ComplexStructureTransformer.transform_contour_sequence(roi)
    
    # Verify structure
    assert "ContourSequence" in result
    assert "ContourCount" in result
    assert result["ContourCount"] == 1
    assert len(result["ContourSequence"]) == 1
    
    # Verify contour transformation
    contour = result["ContourSequence"][0]
    assert "ContourData" in contour
    assert "NumberOfContourPoints" in contour
    
    # Verify coordinate data
    coords = contour["ContourData"]
    assert len(coords) == 9  # 3 points * 3 coordinates
    assert coords == [0.0, 0.0, 0.0, 10.0, 0.0, 0.0, 5.0, 10.0, 0.0]
    
    # Verify point count
    assert contour["NumberOfContourPoints"] == 3
    
    # Verify other fields
    assert contour["ContourGeometricType"] == "CLOSED_PLANAR"
    assert contour["SliceThickness"] == 2.5


def test_transform_contour_sequence_with_real_data(pinnacle_data):
    """Test contour sequence transformation with real pinnacle_io data."""
    rois = pinnacle_data['rois']
    if not rois:
        pytest.skip("No ROI data available in test dataset")
    
    # Test transformation on first ROI with contours
    roi_with_contours = None
    for roi in rois:
        if hasattr(roi, 'curve_list') and roi.curve_list:
            roi_with_contours = roi
            break
    
    if roi_with_contours is None:
        pytest.skip("No ROI with contour data available in test dataset")
    
    # Test transformation
    result = ComplexStructureTransformer.transform_contour_sequence(roi_with_contours)
    
    # Verify basic structure
    assert "ContourSequence" in result
    assert "ContourCount" in result
    assert result["ContourCount"] == len(roi_with_contours.curve_list)
    
    # Verify each contour has required structure
    for contour in result["ContourSequence"]:
        assert "ContourData" in contour
        assert "NumberOfContourPoints" in contour
        
        # Verify coordinate data consistency
        coords = contour["ContourData"]
        if coords:  # May be empty if transformation failed
            assert len(coords) % 3 == 0  # Must be divisible by 3
            expected_points = len(coords) // 3
            assert contour["NumberOfContourPoints"] == expected_points


def test_energy_parsing():
    """Test energy name parsing functionality."""
    transformer = ComplexStructureTransformer()
    
    # Test various energy formats
    assert transformer._parse_energy("6X") == 6.0
    assert transformer._parse_energy("18X") == 18.0
    assert transformer._parse_energy("6E") == 6.0
    assert transformer._parse_energy("15E") == 15.0
    assert transformer._parse_energy("6.5X") == 6.5
    
    # Test edge cases
    assert transformer._parse_energy("") == 6.0  # Default fallback
    assert transformer._parse_energy("ABC") == 6.0  # Invalid format
    assert transformer._parse_energy(None) == 6.0  # None input


def test_validation_functions():
    """Test the validation functions for transformations."""
    transformer = ComplexStructureTransformer()
    
    # Test beam validation
    valid_beam = {
        "Number": 1,
        "Energy": 6.0,
        "Gantry": 0.0,
        "Collimator": 0.0,
        "Couch": 0.0
    }
    issues = transformer.validate_beam_transformation({}, valid_beam)
    assert issues == []
    
    # Test beam with issues
    invalid_beam = {
        "Number": 1,
        "Energy": -1.0,  # Invalid negative energy
        "Gantry": 400.0  # Invalid angle > 360
    }
    issues = transformer.validate_beam_transformation({}, invalid_beam)
    assert len(issues) > 0
    assert any("energy" in issue.lower() for issue in issues)
    assert any("gantry" in issue.lower() for issue in issues)
    
    # Test dose grid validation
    valid_dose_grid = {
        "DoseGrid": {
            "Spacing": [2.5, 2.5, 2.5],
            "Dimensions": [100, 100, 50],
            "Origin": [0.0, 0.0, 0.0]
        }
    }
    issues = transformer.validate_dose_grid_transformation({}, valid_dose_grid)
    assert issues == []
    
    # Test dose grid with issues
    invalid_dose_grid = {
        "DoseGrid": {
            "Spacing": [2.5, -1.0, 2.5],  # Invalid negative spacing
            "Dimensions": [100, 0, 50],   # Invalid zero dimension
            "Origin": [0.0, 0.0]          # Wrong array length
        }
    }
    issues = transformer.validate_dose_grid_transformation({}, invalid_dose_grid)
    assert len(issues) > 0
    
    # Test contour validation
    valid_contour = {
        "ContourSequence": [
            {
                "ContourData": [0.0, 0.0, 0.0, 1.0, 1.0, 0.0, 2.0, 0.0, 0.0],
                "NumberOfContourPoints": 3
            }
        ]
    }
    issues = transformer.validate_contour_transformation({}, valid_contour)
    assert issues == []
    
    # Test contour with issues
    invalid_contour = {
        "ContourSequence": [
            {
                "ContourData": [0.0, 0.0, 0.0, 1.0],  # Not divisible by 3
                "NumberOfContourPoints": 2             # Mismatch with actual points
            }
        ]
    }
    issues = transformer.validate_contour_transformation({}, invalid_contour)
    assert len(issues) > 0


def test_empty_data_handling():
    """Test handling of empty or missing data."""
    transformer = ComplexStructureTransformer()
    
    # Test empty beam list
    trial_no_beams = Mock()
    trial_no_beams.beam_list = []
    result = transformer.transform_beam_list(trial_no_beams)
    assert result["BeamList"] == []
    assert result["BeamCount"] == 0
    
    # Test no dose grid
    trial_no_dose = Mock()
    trial_no_dose.dose_grid = None
    result = transformer.transform_dose_grid(trial_no_dose)
    assert result["DoseGrid"] is None
    assert result["HasDoseGrid"] is False
    
    # Test no contours
    roi_no_contours = Mock()
    roi_no_contours.curve_list = []
    result = transformer.transform_contour_sequence(roi_no_contours)
    assert result["ContourSequence"] == []
    assert result["ContourCount"] == 0


def test_binary_contour_data_unpacking():
    """Test binary contour data unpacking functionality."""
    transformer = ComplexStructureTransformer()
    
    # Create test binary data
    test_coords = [1.0, 2.0, 3.0, 4.0, 5.0, 6.0]
    binary_data = struct.pack('<6f', *test_coords)
    
    # Test unpacking
    result = transformer._unpack_contour_points(binary_data)
    assert result is not None
    assert len(result) == 6
    assert result == test_coords
    
    # Test invalid data
    invalid_data = b"invalid"
    result = transformer._unpack_contour_points(invalid_data)
    assert result is None
    
    # Test empty data
    result = transformer._unpack_contour_points(b"")
    assert result is None


if __name__ == "__main__":
    """Run tests manually for development."""
    test_archive = Path(__file__).parent.parent / "test_data" / "archive_01"
    
    print("Testing ComplexStructureTransformer...")
    
    # Run mock data tests
    test_transform_beam_list_with_mock_data()
    test_transform_dose_grid_with_mock_data()
    test_transform_contour_sequence_with_mock_data()
    test_energy_parsing()
    test_validation_functions()
    test_empty_data_handling()
    test_binary_contour_data_unpacking()
    
    print("Mock data tests completed!")
    
    # Run real data tests if available
    if test_archive.exists():
        from tests.data_mapping.test_pinnacle_data_formatter import load_test_data
        
        try:
            data = load_test_data(test_archive)
            test_transform_beam_list_with_real_data(data)
            test_transform_dose_grid_with_real_data(data)
            test_transform_contour_sequence_with_real_data(data)
            print("Real data tests completed!")
        except Exception as e:
            print(f"Real data tests skipped: {e}")
    else:
        print("Real data tests skipped - no test archive found")
        
    print("All tests completed successfully!")